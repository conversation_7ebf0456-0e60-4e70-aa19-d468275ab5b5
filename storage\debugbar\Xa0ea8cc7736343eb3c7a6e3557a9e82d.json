{"__meta": {"id": "Xa0ea8cc7736343eb3c7a6e3557a9e82d", "datetime": "2025-05-31 00:38:49", "utime": **********.565516, "method": "GET", "uri": "/Admin-panel/test-path", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748648323.944285, "end": **********.565566, "duration": 5.621281147003174, "duration_str": "5.62s", "measures": [{"label": "Booting", "start": 1748648323.944285, "relative_start": 0, "end": **********.67896, "relative_end": **********.67896, "duration": 2.734675168991089, "duration_str": "2.73s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.678999, "relative_start": 2.****************, "end": **********.565573, "relative_end": 6.9141387939453125e-06, "duration": 2.****************, "duration_str": "2.89s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET test-path", "middleware": "web", "uses": "Closure() {#1404\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#1371 …}\n  file: \"C:\\xampp\\htdocs\\Admin-panel\\routes\\web.php\"\n  line: \"185 to 207\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Froutes%2Fweb.php&line=185\" onclick=\"\">routes/web.php:185-207</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "118ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.743481, "duration": 0.*****************, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "localization:24", "source": "middleware::localization:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FLocalization.php&line=24", "ajax": false, "filename": "Localization.php", "line": "24"}, "connection": "dease_test", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\BusinessSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "muRTgZWLZp8TXpCPOqELZrYZSwk3sM8lNAZ03Fak", "landing_site_direction": "ltr", "_previous": "array:1 [\n  \"url\" => \"http://localhost/Admin-panel/test-path\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/test-path", "status_code": "<pre class=sf-dump id=sf-dump-1255092684 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1255092684\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1023192281 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1023192281\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-111651981 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-111651981\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1849497222 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,fr-DZ;q=0.8,fr;q=0.7,ar-DZ;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1274 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5jRlo5KzllMDgvcERoQklrT3VUYkE9PSIsInZhbHVlIjoiVVFoUkF1aUFCQ1pIQjcxVWRKN0Y0bnJabkgrWFEwckhLVmF1NnQ3YkpPblJZVmhCL05ZK2h4c3Q3VGZVeWErRkNGeDJDY0RFMCt6NnM1d1E4cVlxRUpKN3lCUVFheWxUaXlCVWdpb21pRThQbmRyU3E0MEpvQlVoYVo3a25SeDNnaGlZR3B6YTJFc3J0KytQRlpHSE1CakpzbGR2OXRPeTN6ZmNUWmI4N2xnUlZCRW1LeHlPWElHcmthb3NkSnJ0R2NqUlUwTWZseW1zVzgrcTg3WEpzajFpWGdUMFZQMGd0eGN6ZlFDUWFBbz0iLCJtYWMiOiI1MjUzMzQ0OGQ1NTE1NTBkNzI5ZjQwZWE4OTFiNDJiNTM2ZjBiYjg4NTkxNDBhZjI4MjEwZmIwMzg3NzQ2ZWNhIiwidGFnIjoiIn0%3D; SL_G_WPT_TO=fr; XSRF-TOKEN=eyJpdiI6Ijh4RGQxRkEra1FuWVhNUFUwTHZ6eWc9PSIsInZhbHVlIjoiL2JFZmhUQjB5SnRNQ0lQSkh2L083WGpZTFVGV25WUmlTcFAremZ1RXk3ano5QlU4R0lpbmNoNkhTNCtlRHE4b1VYUG0rZFQyUytlVkxydVV4R1o0RWtIK2czNC9yc1Fuc1EyRDdoc2UzYmFKYjBHd0lpWDY1NHNKMHJxWkxsUWYiLCJtYWMiOiI4MjBhYjZlZjU0MWRlMjc3YzFjNWYyODI5YmJmOGI1MDEyNWYxMjVlMzY5YmZmZDgxMmM4NGJlY2YwMGRhNjFlIiwidGFnIjoiIn0%3D; deaseapp_session=eyJpdiI6IjJySGVWeU5wQklneEVqQyt2dWFjR3c9PSIsInZhbHVlIjoiUUlFOFhFcTd2dHBFSGc3WVNMaHY5dDhBVXRYckZLbStjOUhyYlZzTWZQMmJBOVp5eVp2YWZCOU0zeWRDU2NBeTNLT21vQVd0N1NrdXJrVGVHK1NCdDIrOUV4QlAwUGNKN3p2R1d6UC9heXFTZGw5eWRUTXo5UG1oSjBTNmZNKzQiLCJtYWMiOiJmMDRlMWM1MWUzYzIwZWI5OWJkNTUzMGVhZjgzZTNjNTNiNTAyMmI5ZjNhYjUyOTUwNTAxYjUzYTc4YjUxNDU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1849497222\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>SL_G_WPT_TO</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">muRTgZWLZp8TXpCPOqELZrYZSwk3sM8lNAZ03Fak</span>\"\n  \"<span class=sf-dump-key>deaseapp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kBiAZ2CE1aEICB91UULNOk2vJpwX5OXi3AQPOsTY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1027685802 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 30 May 2025 23:38:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"414 characters\">XSRF-TOKEN=eyJpdiI6IlhuNFc2R1VoNUlUOGZsVldZQStHT1E9PSIsInZhbHVlIjoidzhtTDBQc0tGZXNOZE9qbXN0em12RmR3RVBoNElmWEdRcDFPYVdYU1hoMU42dTc3dlR2ZEx5THZ6eTVIRGIvVjZDVW5LaWtVRUlINnlTU1I0Zmg0ek5CcHFzd2pGMnZQVHZOZUp3UmRBNXV4blhjS2JlbzJ3M2tXUWc1Y05VczIiLCJtYWMiOiJmMTM0MGIxZWViNWU5ZjI0YjgwOWVlMGYxOTRjZGQyZjA0M2NkZmFlNTI3NTY4YmEwMWI2NzAyMjAxNDBhNzJiIiwidGFnIjoiIn0%3D; expires=Sat, 31 May 2025 01:38:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"430 characters\">deaseapp_session=eyJpdiI6IllUT0FKazNoU3labWdzM3RYaEkvR2c9PSIsInZhbHVlIjoiMjQ2U2tjS3lEN1IzUS9ISlVadnBGeDFBNDh3VUR6d20xYit3Tis3SkJDR2tMMUsyRld4a2xXaWE0VHROcmFqZVlxMFlvOE5kOWU2WUN1L1ltV0FQamM2VisvZ1dSWUs5blhRYldmeHRkN1ludCtOWDBydTQvNDJUbytlU0oyMDkiLCJtYWMiOiIxNWQ5ZjBmMzMzN2Q5ZjQzZjQxZmNjNDQ4ZmI2OTg1ODNjYjQyOTA3MTJkOTg0MGU2ODNjMWM1MWIxODIzZWU5IiwidGFnIjoiIn0%3D; expires=Sat, 31 May 2025 01:38:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlhuNFc2R1VoNUlUOGZsVldZQStHT1E9PSIsInZhbHVlIjoidzhtTDBQc0tGZXNOZE9qbXN0em12RmR3RVBoNElmWEdRcDFPYVdYU1hoMU42dTc3dlR2ZEx5THZ6eTVIRGIvVjZDVW5LaWtVRUlINnlTU1I0Zmg0ek5CcHFzd2pGMnZQVHZOZUp3UmRBNXV4blhjS2JlbzJ3M2tXUWc1Y05VczIiLCJtYWMiOiJmMTM0MGIxZWViNWU5ZjI0YjgwOWVlMGYxOTRjZGQyZjA0M2NkZmFlNTI3NTY4YmEwMWI2NzAyMjAxNDBhNzJiIiwidGFnIjoiIn0%3D; expires=Sat, 31-May-2025 01:38:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">deaseapp_session=eyJpdiI6IllUT0FKazNoU3labWdzM3RYaEkvR2c9PSIsInZhbHVlIjoiMjQ2U2tjS3lEN1IzUS9ISlVadnBGeDFBNDh3VUR6d20xYit3Tis3SkJDR2tMMUsyRld4a2xXaWE0VHROcmFqZVlxMFlvOE5kOWU2WUN1L1ltV0FQamM2VisvZ1dSWUs5blhRYldmeHRkN1ludCtOWDBydTQvNDJUbytlU0oyMDkiLCJtYWMiOiIxNWQ5ZjBmMzMzN2Q5ZjQzZjQxZmNjNDQ4ZmI2OTg1ODNjYjQyOTA3MTJkOTg0MGU2ODNjMWM1MWIxODIzZWU5IiwidGFnIjoiIn0%3D; expires=Sat, 31-May-2025 01:38:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027685802\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1840729454 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">muRTgZWLZp8TXpCPOqELZrYZSwk3sM8lNAZ03Fak</span>\"\n  \"<span class=sf-dump-key>landing_site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://localhost/Admin-panel/test-path</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1840729454\", {\"maxDepth\":0})</script>\n"}}