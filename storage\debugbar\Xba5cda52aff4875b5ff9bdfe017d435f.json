{"__meta": {"id": "Xba5cda52aff4875b5ff9bdfe017d435f", "datetime": "2025-05-31 00:25:35", "utime": **********.432993, "method": "GET", "uri": "/Admin-panel/test-path", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748647532.061534, "end": **********.433019, "duration": 3.3714849948883057, "duration_str": "3.37s", "measures": [{"label": "Booting", "start": 1748647532.061534, "relative_start": 0, "end": **********.760998, "relative_end": **********.760998, "duration": 2.6994640827178955, "duration_str": "2.7s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.761029, "relative_start": 2.****************, "end": **********.433022, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "672ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET test-path", "middleware": "web", "uses": "Closure() {#1404\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#1371 …}\n  file: \"C:\\xampp\\htdocs\\Admin-panel\\routes\\web.php\"\n  line: \"185 to 207\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Froutes%2Fweb.php&line=185\" onclick=\"\">routes/web.php:185-207</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "124ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.838419, "duration": 0.*****************, "duration_str": "124ms", "memory": 0, "memory_str": null, "filename": "localization:24", "source": "middleware::localization:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FLocalization.php&line=24", "ajax": false, "filename": "Localization.php", "line": "24"}, "connection": "dease_test", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\BusinessSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "muRTgZWLZp8TXpCPOqELZrYZSwk3sM8lNAZ03Fak", "landing_site_direction": "ltr", "_previous": "array:1 [\n  \"url\" => \"http://localhost/Admin-panel/test-path\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/test-path", "status_code": "<pre class=sf-dump id=sf-dump-2062855137 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2062855137\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1895025336 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1895025336\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2126293952 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2126293952\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-98411529 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,fr-DZ;q=0.8,fr;q=0.7,ar-DZ;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1258 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5jRlo5KzllMDgvcERoQklrT3VUYkE9PSIsInZhbHVlIjoiVVFoUkF1aUFCQ1pIQjcxVWRKN0Y0bnJabkgrWFEwckhLVmF1NnQ3YkpPblJZVmhCL05ZK2h4c3Q3VGZVeWErRkNGeDJDY0RFMCt6NnM1d1E4cVlxRUpKN3lCUVFheWxUaXlCVWdpb21pRThQbmRyU3E0MEpvQlVoYVo3a25SeDNnaGlZR3B6YTJFc3J0KytQRlpHSE1CakpzbGR2OXRPeTN6ZmNUWmI4N2xnUlZCRW1LeHlPWElHcmthb3NkSnJ0R2NqUlUwTWZseW1zVzgrcTg3WEpzajFpWGdUMFZQMGd0eGN6ZlFDUWFBbz0iLCJtYWMiOiI1MjUzMzQ0OGQ1NTE1NTBkNzI5ZjQwZWE4OTFiNDJiNTM2ZjBiYjg4NTkxNDBhZjI4MjEwZmIwMzg3NzQ2ZWNhIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkVBNW50TlhvYWhrRzRyK0VUcXJkRVE9PSIsInZhbHVlIjoiRExTZHJ1RE83am5yckR4NDE3WkZoV25PUld4a3IyTXhzV3ZwaVRraHI5R0dIYkhST1A4UUZOT0w4UURRUEtuQStrRHZlQStyQk0yZ2plWnRsSUp0c0NhcVpFT21HdG5EMEI2bzZRUU9DN1pvSmxtTHdlY3pOKzkvMWxWZ1VhV0oiLCJtYWMiOiJjNjBhMWZlNDAyZWFiZDJmMTg5YTVjZmE2YzAxYzk0ZGVhYWRjMDI4ZDk0MWIwZjAwMTVlMzg2NjBjNDY0NjEyIiwidGFnIjoiIn0%3D; deaseapp_session=eyJpdiI6InEraURUbElCbXNlK0xQTEkwNERZV0E9PSIsInZhbHVlIjoiMXBpeEdvL0V0OEZneG9jQWpodDBWMk55dGpHVUZ3OWMvNDVxMmdRRGU1YjVvcWZsaEdXR2I0UGVVZ0tNQmtaa0RoZlFObkRkL1ArS1JKQXZ0NG1xdUlUVHJtWG9XRjA1QUVTSllKYzUwSnlaZ3htbUdNVW4xYjhvMmFWYTZsdzciLCJtYWMiOiJlN2Y1OTVhMjUxNjk3MTUxZDAxMjVmMWFlZTY2ODIxZDRkNjI4N2JmZDFiY2E5Y2ZjMzQyMDA1NmZkMDBhNGY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-98411529\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-989453888 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">muRTgZWLZp8TXpCPOqELZrYZSwk3sM8lNAZ03Fak</span>\"\n  \"<span class=sf-dump-key>deaseapp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kBiAZ2CE1aEICB91UULNOk2vJpwX5OXi3AQPOsTY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-989453888\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-215963679 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 30 May 2025 23:25:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"414 characters\">XSRF-TOKEN=eyJpdiI6IlUrWTVOeUttS0tBVU4vNS9vTktnM0E9PSIsInZhbHVlIjoic2U4cElxeThGMWlMbllPRXI5QUdqZ1dzSXdHSUtCM0FQRWV3am9WWXlDNlZ6QmFjc29PN1dVckRGaVl4UFJrNmtxUVkwdXhwdy9nU2ZqNll3OU9KN3g1SEpKaTQxTW51aHpBVFZHZUxyN1c3R1BuTmlRS2grcnFCeXJESGdkWTgiLCJtYWMiOiJkZmUwOGZhYmJmZGU5MmEzYTIzMmJlMjkwODEyZTM2ZjFjNmU0YWRkMTc3ZjFhOWJiN2M1MWFmNWE0MmI3MjcwIiwidGFnIjoiIn0%3D; expires=Sat, 31 May 2025 01:25:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"430 characters\">deaseapp_session=eyJpdiI6ImdLVzdWQ3BCbFpod0RQWnNUNEJ4MHc9PSIsInZhbHVlIjoiZEJUWFFxZG5Pa200VmQzdXp6RERWUE43d2lNR0c2K2FhOWFyalhBYTg4eDNZM2ZzTEk5aUlYZ1pUbFNyeTkvc01xbnB3ZUZGRzhydjg2K21zTU91VHUzU0ZRdVJjbnIrd3l1YnBiVWp1Ym15U1RPamFPcS83NGxvNmZvUGxlMk8iLCJtYWMiOiI1MDZkYzE1MzI5OGIxY2M5MzY1NWI0N2EwNjQ2MDZlYmZlMDkzMGNiMDQwYzU2YzExM2MzZGVkODNhM2YwNDE4IiwidGFnIjoiIn0%3D; expires=Sat, 31 May 2025 01:25:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlUrWTVOeUttS0tBVU4vNS9vTktnM0E9PSIsInZhbHVlIjoic2U4cElxeThGMWlMbllPRXI5QUdqZ1dzSXdHSUtCM0FQRWV3am9WWXlDNlZ6QmFjc29PN1dVckRGaVl4UFJrNmtxUVkwdXhwdy9nU2ZqNll3OU9KN3g1SEpKaTQxTW51aHpBVFZHZUxyN1c3R1BuTmlRS2grcnFCeXJESGdkWTgiLCJtYWMiOiJkZmUwOGZhYmJmZGU5MmEzYTIzMmJlMjkwODEyZTM2ZjFjNmU0YWRkMTc3ZjFhOWJiN2M1MWFmNWE0MmI3MjcwIiwidGFnIjoiIn0%3D; expires=Sat, 31-May-2025 01:25:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">deaseapp_session=eyJpdiI6ImdLVzdWQ3BCbFpod0RQWnNUNEJ4MHc9PSIsInZhbHVlIjoiZEJUWFFxZG5Pa200VmQzdXp6RERWUE43d2lNR0c2K2FhOWFyalhBYTg4eDNZM2ZzTEk5aUlYZ1pUbFNyeTkvc01xbnB3ZUZGRzhydjg2K21zTU91VHUzU0ZRdVJjbnIrd3l1YnBiVWp1Ym15U1RPamFPcS83NGxvNmZvUGxlMk8iLCJtYWMiOiI1MDZkYzE1MzI5OGIxY2M5MzY1NWI0N2EwNjQ2MDZlYmZlMDkzMGNiMDQwYzU2YzExM2MzZGVkODNhM2YwNDE4IiwidGFnIjoiIn0%3D; expires=Sat, 31-May-2025 01:25:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-215963679\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1236057716 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">muRTgZWLZp8TXpCPOqELZrYZSwk3sM8lNAZ03Fak</span>\"\n  \"<span class=sf-dump-key>landing_site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://localhost/Admin-panel/test-path</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1236057716\", {\"maxDepth\":0})</script>\n"}}