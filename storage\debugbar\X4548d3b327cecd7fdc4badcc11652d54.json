{"__meta": {"id": "X4548d3b327cecd7fdc4badcc11652d54", "datetime": "2025-05-31 00:43:20", "utime": 1748648600.232777, "method": "GET", "uri": "/Admin-panel/", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748648587.10524, "end": 1748648600.232825, "duration": 13.12758493423462, "duration_str": "13.13s", "measures": [{"label": "Booting", "start": 1748648587.10524, "relative_start": 0, "end": **********.826266, "relative_end": **********.826266, "duration": 2.7210259437561035, "duration_str": "2.72s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.826324, "relative_start": 2.721083879470825, "end": 1748648600.232831, "relative_end": 5.9604644775390625e-06, "duration": 10.406507015228271, "duration_str": "10.41s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54494936, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "home", "param_count": null, "params": [], "start": 1748648592.141955, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/home.blade.phphome", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}}, {"name": "layouts.landing.app", "param_count": null, "params": [], "start": 1748648596.465205, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.phplayouts.landing.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\HomeController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "home", "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=34\" onclick=\"\">app/Http/Controllers/HomeController.php:34-124</a>"}, "queries": {"nb_statements": 53, "nb_failed_statements": 0, "accumulated_duration": 6.25036, "accumulated_duration_str": "6.25s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.91111, "duration": 0.11449, "duration_str": "114ms", "memory": 0, "memory_str": null, "filename": "localization:24", "source": "middleware::localization:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FLocalization.php&line=24", "ajax": false, "filename": "Localization.php", "line": "24"}, "connection": "dease_test", "start_percent": 0, "width_percent": 1.832}, {"sql": "select * from `data_settings` where `type` = 'admin_landing_page'", "type": "query", "params": [], "bindings": ["admin_landing_page"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.053573, "duration": 0.11518, "duration_str": "115ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:36", "source": "app/Http/Controllers/HomeController.php:36", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=36", "ajax": false, "filename": "HomeController.php", "line": "36"}, "connection": "dease_test", "start_percent": 1.832, "width_percent": 1.843}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 87, 88) and `translations`.`translationable_type` = 'App\\Models\\DataSetting' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\DataSetting", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.190639, "duration": 0.11711, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:36", "source": "app/Http/Controllers/HomeController.php:36", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=36", "ajax": false, "filename": "HomeController.php", "line": "36"}, "connection": "dease_test", "start_percent": 3.675, "width_percent": 1.874}, {"sql": "select * from `business_settings` where `key` = 'opening_time' limit 1", "type": "query", "params": [], "bindings": ["opening_time"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.323426, "duration": 0.11599, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:58", "source": "app/Http/Controllers/HomeController.php:58", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=58", "ajax": false, "filename": "HomeController.php", "line": "58"}, "connection": "dease_test", "start_percent": 5.548, "width_percent": 1.856}, {"sql": "select * from `business_settings` where `key` = 'closing_time' limit 1", "type": "query", "params": [], "bindings": ["closing_time"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4527538, "duration": 0.11508, "duration_str": "115ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:59", "source": "app/Http/Controllers/HomeController.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=59", "ajax": false, "filename": "HomeController.php", "line": "59"}, "connection": "dease_test", "start_percent": 7.404, "width_percent": 1.841}, {"sql": "select * from `business_settings` where `key` = 'opening_day' limit 1", "type": "query", "params": [], "bindings": ["opening_day"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 60}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5836022, "duration": 0.11948, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:60", "source": "app/Http/Controllers/HomeController.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=60", "ajax": false, "filename": "HomeController.php", "line": "60"}, "connection": "dease_test", "start_percent": 9.245, "width_percent": 1.912}, {"sql": "select * from `business_settings` where `key` = 'closing_day' limit 1", "type": "query", "params": [], "bindings": ["closing_day"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 61}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.71504, "duration": 0.*****************, "duration_str": "129ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:61", "source": "app/Http/Controllers/HomeController.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=61", "ajax": false, "filename": "HomeController.php", "line": "61"}, "connection": "dease_test", "start_percent": 11.157, "width_percent": 2.067}, {"sql": "select * from `admin_promotional_banners`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 62}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.851838, "duration": 0.11831, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:62", "source": "app/Http/Controllers/HomeController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=62", "ajax": false, "filename": "HomeController.php", "line": "62"}, "connection": "dease_test", "start_percent": 13.224, "width_percent": 1.893}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1) and `translations`.`translationable_type` = 'App\\Models\\AdminPromotionalBanner' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\AdminPromotionalBanner", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 62}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.9804468, "duration": 0.11769, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:62", "source": "app/Http/Controllers/HomeController.php:62", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=62", "ajax": false, "filename": "HomeController.php", "line": "62"}, "connection": "dease_test", "start_percent": 15.117, "width_percent": 1.883}, {"sql": "select * from `admin_features`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.105657, "duration": 0.11599, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:63", "source": "app/Http/Controllers/HomeController.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=63", "ajax": false, "filename": "HomeController.php", "line": "63"}, "connection": "dease_test", "start_percent": 17, "width_percent": 1.856}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1) and `translations`.`translationable_type` = 'App\\Models\\AdminFeature' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\AdminFeature", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 63}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2313218, "duration": 0.11892, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:63", "source": "app/Http/Controllers/HomeController.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=63", "ajax": false, "filename": "HomeController.php", "line": "63"}, "connection": "dease_test", "start_percent": 18.855, "width_percent": 1.903}, {"sql": "select * from `admin_special_criterias`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 64}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.356204, "duration": 0.11731, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:64", "source": "app/Http/Controllers/HomeController.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=64", "ajax": false, "filename": "HomeController.php", "line": "64"}, "connection": "dease_test", "start_percent": 20.758, "width_percent": 1.877}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1) and `translations`.`translationable_type` = 'App\\Models\\AdminSpecialCriteria' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\AdminSpecialCriteria", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 64}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.4857612, "duration": 0.11581, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:64", "source": "app/Http/Controllers/HomeController.php:64", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=64", "ajax": false, "filename": "HomeController.php", "line": "64"}, "connection": "dease_test", "start_percent": 22.635, "width_percent": 1.853}, {"sql": "select * from `admin_testimonials`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 65}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.614872, "duration": 0.11494, "duration_str": "115ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:65", "source": "app/Http/Controllers/HomeController.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=65", "ajax": false, "filename": "HomeController.php", "line": "65"}, "connection": "dease_test", "start_percent": 24.488, "width_percent": 1.839}, {"sql": "select * from `business_settings` where `key` = 'landing_page' limit 1", "type": "query", "params": [], "bindings": ["landing_page"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 938}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.739398, "duration": 0.11672, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "helpers.php:938", "source": "app/CentralLogics/helpers.php:938", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=938", "ajax": false, "filename": "helpers.php", "line": "938"}, "connection": "dease_test", "start_percent": 26.327, "width_percent": 1.867}, {"sql": "select * from `business_settings` where `key` = 'landing_integration_type' limit 1", "type": "query", "params": [], "bindings": ["landing_integration_type"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 951}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.868124, "duration": 0.11785, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "helpers.php:951", "source": "app/CentralLogics/helpers.php:951", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=951", "ajax": false, "filename": "helpers.php", "line": "951"}, "connection": "dease_test", "start_percent": 28.194, "width_percent": 1.885}, {"sql": "select * from `business_settings` where `key` = 'landing_page_custom_url' limit 1", "type": "query", "params": [], "bindings": ["landing_page_custom_url"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 951}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Controllers\\HomeController.php", "line": 112}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9996269, "duration": 0.11712, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "helpers.php:951", "source": "app/CentralLogics/helpers.php:951", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=951", "ajax": false, "filename": "helpers.php", "line": "951"}, "connection": "dease_test", "start_percent": 30.08, "width_percent": 1.874}, {"sql": "select * from `business_settings` where (`key` = 'business_name') limit 1", "type": "query", "params": [], "bindings": ["business_name"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2268}, {"index": 17, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/home.blade.php", "line": 2}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1748648592.158206, "duration": 0.1221, "duration_str": "122ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2268", "source": "app/CentralLogics/helpers.php:2268", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2268", "ajax": false, "filename": "helpers.php", "line": "2268"}, "connection": "dease_test", "start_percent": 31.953, "width_percent": 1.953}, {"sql": "select * from `business_settings` where (`key` = 'business_name') limit 1", "type": "query", "params": [], "bindings": ["business_name"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2268}, {"index": 17, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/home.blade.php", "line": 2}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1748648592.2911, "duration": 0.11533, "duration_str": "115ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2268", "source": "app/CentralLogics/helpers.php:2268", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2268", "ajax": false, "filename": "helpers.php", "line": "2268"}, "connection": "dease_test", "start_percent": 33.907, "width_percent": 1.845}, {"sql": "select * from `business_settings` where (`key` = 'front_end_url') limit 1", "type": "query", "params": [], "bindings": ["front_end_url"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/home.blade.php", "line": 6}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648592.418436, "duration": 0.11655, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "home:6", "source": "view::home:6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fhome.blade.php&line=6", "ajax": false, "filename": "home.blade.php", "line": "6"}, "connection": "dease_test", "start_percent": 35.752, "width_percent": 1.865}, {"sql": "select * from `business_settings` where (`key` = 'landing_page_text') limit 1", "type": "query", "params": [], "bindings": ["landing_page_text"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/home.blade.php", "line": 8}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648592.548423, "duration": 0.11517000000000001, "duration_str": "115ms", "memory": 0, "memory_str": null, "filename": "home:8", "source": "view::home:8", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fhome.blade.php&line=8", "ajax": false, "filename": "home.blade.php", "line": "8"}, "connection": "dease_test", "start_percent": 37.617, "width_percent": 1.843}, {"sql": "select * from `business_settings` where (`key` = 'landing_page_links') limit 1", "type": "query", "params": [], "bindings": ["landing_page_links"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/home.blade.php", "line": 10}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648592.6727529, "duration": 0.11559, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "home:10", "source": "view::home:10", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fhome.blade.php&line=10", "ajax": false, "filename": "home.blade.php", "line": "10"}, "connection": "dease_test", "start_percent": 39.459, "width_percent": 1.849}, {"sql": "select * from `business_settings` where (`key` = 'landing_page_images') limit 1", "type": "query", "params": [], "bindings": ["landing_page_images"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/home.blade.php", "line": 12}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648592.8010318, "duration": 0.11736, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "home:12", "source": "view::home:12", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fhome.blade.php&line=12", "ajax": false, "filename": "home.blade.php", "line": "12"}, "connection": "dease_test", "start_percent": 41.309, "width_percent": 1.878}, {"sql": "select * from `business_settings` where (`key` = 'logo') limit 1", "type": "query", "params": [], "bindings": ["logo"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/home.blade.php", "line": 15}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648592.9282851, "duration": 0.11686, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "home:15", "source": "view::home:15", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fhome.blade.php&line=15", "ajax": false, "filename": "home.blade.php", "line": "15"}, "connection": "dease_test", "start_percent": 43.186, "width_percent": 1.87}, {"sql": "select * from `modules` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/home.blade.php", "line": 1898}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648594.031984, "duration": 0.12896000000000002, "duration_str": "129ms", "memory": 0, "memory_str": null, "filename": "home:1898", "source": "view::home:1898", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fhome.blade.php&line=1898", "ajax": false, "filename": "home.blade.php", "line": "1898"}, "connection": "dease_test", "start_percent": 45.056, "width_percent": 2.063}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (1, 2, 5, 7) and `translations`.`translationable_type` = 'App\\Models\\Module' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\Module", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/home.blade.php", "line": 1898}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648594.188076, "duration": 0.11706, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "home:1898", "source": "view::home:1898", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fhome.blade.php&line=1898", "ajax": false, "filename": "home.blade.php", "line": "1898"}, "connection": "dease_test", "start_percent": 47.119, "width_percent": 1.873}, {"sql": "select * from `business_settings` where (`key` = 'icon') limit 1", "type": "query", "params": [], "bindings": ["icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "home", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/home.blade.php", "line": 3581}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648596.131752, "duration": 0.11603, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "home:3581", "source": "view::home:3581", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Fhome.blade.php&line=3581", "ajax": false, "filename": "home.blade.php", "line": "3581"}, "connection": "dease_test", "start_percent": 48.992, "width_percent": 1.856}, {"sql": "select * from `business_settings` where `key` = 'country' limit 1", "type": "query", "params": [], "bindings": ["country"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 5}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648596.5114741, "duration": 0.11568, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:5", "source": "view::layouts.landing.app:5", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=5", "ajax": false, "filename": "app.blade.php", "line": "5"}, "connection": "dease_test", "start_percent": 50.848, "width_percent": 1.851}, {"sql": "select * from `business_settings` where (`key` = 'icon') limit 1", "type": "query", "params": [], "bindings": ["icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 26}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648596.638609, "duration": 0.11707, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:26", "source": "view::layouts.landing.app:26", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=26", "ajax": false, "filename": "app.blade.php", "line": "26"}, "connection": "dease_test", "start_percent": 52.699, "width_percent": 1.873}, {"sql": "select * from `business_settings` where (`key` = 'backgroundChange') limit 1", "type": "query", "params": [], "bindings": ["backgroundChange"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648596.7918131, "duration": 0.11618, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:29", "source": "view::layouts.landing.app:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=29", "ajax": false, "filename": "app.blade.php", "line": "29"}, "connection": "dease_test", "start_percent": 54.572, "width_percent": 1.859}, {"sql": "select * from `business_settings` where (`key` = 'landing_page_text') limit 1", "type": "query", "params": [], "bindings": ["landing_page_text"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648596.9216, "duration": 0.1172, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:44", "source": "view::layouts.landing.app:44", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=44", "ajax": false, "filename": "app.blade.php", "line": "44"}, "connection": "dease_test", "start_percent": 56.431, "width_percent": 1.875}, {"sql": "select * from `data_settings` where (`key` = 'fixed_link' and `type` = 'admin_landing_page') limit 1", "type": "query", "params": [], "bindings": ["fixed_link", "admin_landing_page"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648597.0451329, "duration": 0.11612, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:46", "source": "view::layouts.landing.app:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=46", "ajax": false, "filename": "app.blade.php", "line": "46"}, "connection": "dease_test", "start_percent": 58.306, "width_percent": 1.858}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (87) and `translations`.`translationable_type` = 'App\\Models\\DataSetting' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\DataSetting", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 46}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648597.172698, "duration": 0.11688, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:46", "source": "view::layouts.landing.app:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=46", "ajax": false, "filename": "app.blade.php", "line": "46"}, "connection": "dease_test", "start_percent": 60.164, "width_percent": 1.87}, {"sql": "select * from `business_settings` where (`key` = 'icon') limit 1", "type": "query", "params": [], "bindings": ["icon"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 56}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648597.300692, "duration": 0.12004000000000001, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:56", "source": "view::layouts.landing.app:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=56", "ajax": false, "filename": "app.blade.php", "line": "56"}, "connection": "dease_test", "start_percent": 62.034, "width_percent": 1.921}, {"sql": "select * from `business_settings` where (`key` = 'logo') limit 1", "type": "query", "params": [], "bindings": ["logo"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 57}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648597.433599, "duration": 0.1195, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:57", "source": "view::layouts.landing.app:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=57", "ajax": false, "filename": "app.blade.php", "line": "57"}, "connection": "dease_test", "start_percent": 63.954, "width_percent": 1.912}, {"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 99}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648597.706285, "duration": 0.12233, "duration_str": "122ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:99", "source": "view::layouts.landing.app:99", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=99", "ajax": false, "filename": "app.blade.php", "line": "99"}, "connection": "dease_test", "start_percent": 65.866, "width_percent": 1.957}, {"sql": "select * from `data_settings` where (`type` = 'admin_landing_page' and `key` = 'fixed_newsletter_title') limit 1", "type": "query", "params": [], "bindings": ["admin_landing_page", "fixed_newsletter_title"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 173}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648597.86651, "duration": 0.11981, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:173", "source": "view::layouts.landing.app:173", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=173", "ajax": false, "filename": "app.blade.php", "line": "173"}, "connection": "dease_test", "start_percent": 67.823, "width_percent": 1.917}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (11) and `translations`.`translationable_type` = 'App\\Models\\DataSetting' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\DataSetting", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 173}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648597.993577, "duration": 0.11877, "duration_str": "119ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:173", "source": "view::layouts.landing.app:173", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=173", "ajax": false, "filename": "app.blade.php", "line": "173"}, "connection": "dease_test", "start_percent": 69.74, "width_percent": 1.9}, {"sql": "select * from `data_settings` where (`type` = 'admin_landing_page' and `key` = 'fixed_newsletter_sub_title') limit 1", "type": "query", "params": [], "bindings": ["admin_landing_page", "fixed_newsletter_sub_title"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 175}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648598.12186, "duration": 0.1168, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:175", "source": "view::layouts.landing.app:175", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=175", "ajax": false, "filename": "app.blade.php", "line": "175"}, "connection": "dease_test", "start_percent": 71.641, "width_percent": 1.869}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (12) and `translations`.`translationable_type` = 'App\\Models\\DataSetting' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\DataSetting", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 175}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648598.251394, "duration": 0.11724, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:175", "source": "view::layouts.landing.app:175", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=175", "ajax": false, "filename": "app.blade.php", "line": "175"}, "connection": "dease_test", "start_percent": 73.509, "width_percent": 1.876}, {"sql": "select * from `data_settings` where (`type` = 'admin_landing_page' and `key` = 'fixed_footer_article_title') limit 1", "type": "query", "params": [], "bindings": ["admin_landing_page", "fixed_footer_article_title"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 177}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648598.378696, "duration": 0.11666, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:177", "source": "view::layouts.landing.app:177", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=177", "ajax": false, "filename": "app.blade.php", "line": "177"}, "connection": "dease_test", "start_percent": 75.385, "width_percent": 1.866}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (13) and `translations`.`translationable_type` = 'App\\Models\\DataSetting' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\DataSetting", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 177}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648598.50648, "duration": 0.11707, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:177", "source": "view::layouts.landing.app:177", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=177", "ajax": false, "filename": "app.blade.php", "line": "177"}, "connection": "dease_test", "start_percent": 77.251, "width_percent": 1.873}, {"sql": "select * from `social_media` where `status` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 228}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648598.6992788, "duration": 0.12929, "duration_str": "129ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:228", "source": "view::layouts.landing.app:228", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=228", "ajax": false, "filename": "app.blade.php", "line": "228"}, "connection": "dease_test", "start_percent": 79.124, "width_percent": 2.069}, {"sql": "select * from `data_settings` where (`type` = 'admin_landing_page' and `key` = 'download_user_app_links') limit 1", "type": "query", "params": [], "bindings": ["admin_landing_page", "download_user_app_links"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 239}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648598.840867, "duration": 0.12624, "duration_str": "126ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:239", "source": "view::layouts.landing.app:239", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=239", "ajax": false, "filename": "app.blade.php", "line": "239"}, "connection": "dease_test", "start_percent": 81.193, "width_percent": 2.02}, {"sql": "select * from `translations` where `translations`.`translationable_id` in (27) and `translations`.`translationable_type` = 'App\\Models\\DataSetting' and `locale` = 'en'", "type": "query", "params": [], "bindings": ["App\\Models\\DataSetting", "en"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 239}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648598.978457, "duration": 0.11806, "duration_str": "118ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:239", "source": "view::layouts.landing.app:239", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=239", "ajax": false, "filename": "app.blade.php", "line": "239"}, "connection": "dease_test", "start_percent": 83.213, "width_percent": 1.889}, {"sql": "select `value`, `key` from `data_settings` where `type` = 'admin_landing_page' and `key` in ('shipping_policy_status', 'refund_policy_status', 'cancellation_policy_status')", "type": "query", "params": [], "bindings": ["admin_landing_page", "shipping_policy_status", "refund_policy_status", "cancellation_policy_status"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 256}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1748648599.105967, "duration": 0.11631, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "layouts.landing.app:256", "source": "view::layouts.landing.app:256", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fresources%2Fviews%2Flayouts%2Flanding%2Fapp.blade.php&line=256", "ajax": false, "filename": "app.blade.php", "line": "256"}, "connection": "dease_test", "start_percent": 85.101, "width_percent": 1.861}, {"sql": "select * from `business_settings` where (`key` = 'address') limit 1", "type": "query", "params": [], "bindings": ["address"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2268}, {"index": 17, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 296}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1748648599.279749, "duration": 0.1163, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2268", "source": "app/CentralLogics/helpers.php:2268", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2268", "ajax": false, "filename": "helpers.php", "line": "2268"}, "connection": "dease_test", "start_percent": 86.962, "width_percent": 1.861}, {"sql": "select * from `business_settings` where (`key` = 'email_address') limit 1", "type": "query", "params": [], "bindings": ["email_address"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2268}, {"index": 17, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 301}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1748648599.4078808, "duration": 0.11964, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2268", "source": "app/CentralLogics/helpers.php:2268", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2268", "ajax": false, "filename": "helpers.php", "line": "2268"}, "connection": "dease_test", "start_percent": 88.823, "width_percent": 1.914}, {"sql": "select * from `business_settings` where (`key` = 'email_address') limit 1", "type": "query", "params": [], "bindings": ["email_address"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2268}, {"index": 17, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 311}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1748648599.53867, "duration": 0.11654, "duration_str": "117ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2268", "source": "app/CentralLogics/helpers.php:2268", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2268", "ajax": false, "filename": "helpers.php", "line": "2268"}, "connection": "dease_test", "start_percent": 90.737, "width_percent": 1.865}, {"sql": "select * from `business_settings` where (`key` = 'phone') limit 1", "type": "query", "params": [], "bindings": ["phone"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2268}, {"index": 17, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 316}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1748648599.666295, "duration": 0.11627, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2268", "source": "app/CentralLogics/helpers.php:2268", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2268", "ajax": false, "filename": "helpers.php", "line": "2268"}, "connection": "dease_test", "start_percent": 92.602, "width_percent": 1.86}, {"sql": "select * from `business_settings` where (`key` = 'phone') limit 1", "type": "query", "params": [], "bindings": ["phone"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2268}, {"index": 17, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 323}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1748648599.793524, "duration": 0.11452, "duration_str": "115ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2268", "source": "app/CentralLogics/helpers.php:2268", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2268", "ajax": false, "filename": "helpers.php", "line": "2268"}, "connection": "dease_test", "start_percent": 94.462, "width_percent": 1.832}, {"sql": "select * from `business_settings` where (`key` = 'footer_text') limit 1", "type": "query", "params": [], "bindings": ["footer_text"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2268}, {"index": 17, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 331}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1748648599.919646, "duration": 0.1162, "duration_str": "116ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2268", "source": "app/CentralLogics/helpers.php:2268", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2268", "ajax": false, "filename": "helpers.php", "line": "2268"}, "connection": "dease_test", "start_percent": 96.294, "width_percent": 1.859}, {"sql": "select * from `business_settings` where (`key` = 'business_name') limit 1", "type": "query", "params": [], "bindings": ["business_name"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/CentralLogics/helpers.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\CentralLogics\\helpers.php", "line": 2268}, {"index": 17, "namespace": "view", "name": "layouts.landing.app", "file": "C:\\xampp\\htdocs\\Admin-panel\\resources\\views/layouts/landing/app.blade.php", "line": 333}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1748648600.047469, "duration": 0.11543, "duration_str": "115ms", "memory": 0, "memory_str": null, "filename": "helpers.php:2268", "source": "app/CentralLogics/helpers.php:2268", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FCentralLogics%2Fhelpers.php&line=2268", "ajax": false, "filename": "helpers.php", "line": "2268"}, "connection": "dease_test", "start_percent": 98.153, "width_percent": 1.847}]}, "models": {"data": {"App\\Models\\DataSetting": {"value": 37, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FDataSetting.php&line=1", "ajax": false, "filename": "DataSetting.php", "line": "?"}}, "App\\Models\\Translation": {"value": 31, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FTranslation.php&line=1", "ajax": false, "filename": "Translation.php", "line": "?"}}, "App\\Models\\BusinessSetting": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}, "App\\Models\\Module": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FModule.php&line=1", "ajax": false, "filename": "Module.php", "line": "?"}}, "App\\Models\\AdminPromotionalBanner": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdminPromotionalBanner.php&line=1", "ajax": false, "filename": "AdminPromotionalBanner.php", "line": "?"}}, "App\\Models\\AdminFeature": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdminFeature.php&line=1", "ajax": false, "filename": "AdminFeature.php", "line": "?"}}, "App\\Models\\AdminSpecialCriteria": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdminSpecialCriteria.php&line=1", "ajax": false, "filename": "AdminSpecialCriteria.php", "line": "?"}}, "App\\Models\\AdminTestimonial": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FAdminTestimonial.php&line=1", "ajax": false, "filename": "AdminTestimonial.php", "line": "?"}}, "App\\Models\\SocialMedia": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FSocialMedia.php&line=1", "ajax": false, "filename": "SocialMedia.php", "line": "?"}}}, "count": 103, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "muRTgZWLZp8TXpCPOqELZrYZSwk3sM8lNAZ03Fak", "landing_site_direction": "ltr", "_previous": "array:1 [\n  \"url\" => \"http://localhost/Admin-panel\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-459094483 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-459094483\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-281814859 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-281814859\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1999349128 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1999349128\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-805573528 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,fr-DZ;q=0.8,fr;q=0.7,ar-DZ;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1274 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5jRlo5KzllMDgvcERoQklrT3VUYkE9PSIsInZhbHVlIjoiVVFoUkF1aUFCQ1pIQjcxVWRKN0Y0bnJabkgrWFEwckhLVmF1NnQ3YkpPblJZVmhCL05ZK2h4c3Q3VGZVeWErRkNGeDJDY0RFMCt6NnM1d1E4cVlxRUpKN3lCUVFheWxUaXlCVWdpb21pRThQbmRyU3E0MEpvQlVoYVo3a25SeDNnaGlZR3B6YTJFc3J0KytQRlpHSE1CakpzbGR2OXRPeTN6ZmNUWmI4N2xnUlZCRW1LeHlPWElHcmthb3NkSnJ0R2NqUlUwTWZseW1zVzgrcTg3WEpzajFpWGdUMFZQMGd0eGN6ZlFDUWFBbz0iLCJtYWMiOiI1MjUzMzQ0OGQ1NTE1NTBkNzI5ZjQwZWE4OTFiNDJiNTM2ZjBiYjg4NTkxNDBhZjI4MjEwZmIwMzg3NzQ2ZWNhIiwidGFnIjoiIn0%3D; SL_G_WPT_TO=fr; XSRF-TOKEN=eyJpdiI6IlhuNFc2R1VoNUlUOGZsVldZQStHT1E9PSIsInZhbHVlIjoidzhtTDBQc0tGZXNOZE9qbXN0em12RmR3RVBoNElmWEdRcDFPYVdYU1hoMU42dTc3dlR2ZEx5THZ6eTVIRGIvVjZDVW5LaWtVRUlINnlTU1I0Zmg0ek5CcHFzd2pGMnZQVHZOZUp3UmRBNXV4blhjS2JlbzJ3M2tXUWc1Y05VczIiLCJtYWMiOiJmMTM0MGIxZWViNWU5ZjI0YjgwOWVlMGYxOTRjZGQyZjA0M2NkZmFlNTI3NTY4YmEwMWI2NzAyMjAxNDBhNzJiIiwidGFnIjoiIn0%3D; deaseapp_session=eyJpdiI6IllUT0FKazNoU3labWdzM3RYaEkvR2c9PSIsInZhbHVlIjoiMjQ2U2tjS3lEN1IzUS9ISlVadnBGeDFBNDh3VUR6d20xYit3Tis3SkJDR2tMMUsyRld4a2xXaWE0VHROcmFqZVlxMFlvOE5kOWU2WUN1L1ltV0FQamM2VisvZ1dSWUs5blhRYldmeHRkN1ludCtOWDBydTQvNDJUbytlU0oyMDkiLCJtYWMiOiIxNWQ5ZjBmMzMzN2Q5ZjQzZjQxZmNjNDQ4ZmI2OTg1ODNjYjQyOTA3MTJkOTg0MGU2ODNjMWM1MWIxODIzZWU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-805573528\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1981270861 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>SL_G_WPT_TO</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">muRTgZWLZp8TXpCPOqELZrYZSwk3sM8lNAZ03Fak</span>\"\n  \"<span class=sf-dump-key>deaseapp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kBiAZ2CE1aEICB91UULNOk2vJpwX5OXi3AQPOsTY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1981270861\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2108678332 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 30 May 2025 23:43:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"414 characters\">XSRF-TOKEN=eyJpdiI6IjkvRDZpTWt5RGhCT2hFNkJsK0gwY2c9PSIsInZhbHVlIjoiK2N3alo5YW5SQlN5Ym00Qzl3MHF5SkxVWjhDM3M0VkVQUTJ5RGRIbFBUUVY5NkxNVW1mVVVtUlhNa0ozU0dYQitGd1dGelppL3ZrNUVYc1llTGthVnBCUEtTTXNGUEtYTEJTNDNBc1BkL3dmelFSUXVEU1FZU3VMQWlISVgyUEwiLCJtYWMiOiJjZTYyNGE0Y2QxODBjODVlYjgxNmFkOGY2YzdiZGFhOTY4NTQyMDcxYjJiZjNkNjFhNzczNjQxY2MwYmQ2ZmRhIiwidGFnIjoiIn0%3D; expires=Sat, 31 May 2025 01:43:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"430 characters\">deaseapp_session=eyJpdiI6IkRrd3EvemlIaTEvWWQ3dzJnZlZtVEE9PSIsInZhbHVlIjoiSXAvQ3pCQjBvcitXa1dyQXhsUTFoWTdWZkU0elVFUmk3dFdGRUdCMkI5amI3TTgzc0VsSzRnaFdoS3M0NUhnTFRCdElCZVFUbFJlUTlKTzlhQkthcnNxYnpVR2tZR2xyTFpSRE9nZSsrUTRKVWNQa2pPVWk5MEVkN3RRZ3V1T2kiLCJtYWMiOiJhZTVlY2M4OWUwOTMzOTkwMmVjNzE1YjAxZDhkMzkyNDYyNDI5Yzk0MDdjNDVmN2M5MmIzNjdhMTM2YjUyOTNiIiwidGFnIjoiIn0%3D; expires=Sat, 31 May 2025 01:43:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjkvRDZpTWt5RGhCT2hFNkJsK0gwY2c9PSIsInZhbHVlIjoiK2N3alo5YW5SQlN5Ym00Qzl3MHF5SkxVWjhDM3M0VkVQUTJ5RGRIbFBUUVY5NkxNVW1mVVVtUlhNa0ozU0dYQitGd1dGelppL3ZrNUVYc1llTGthVnBCUEtTTXNGUEtYTEJTNDNBc1BkL3dmelFSUXVEU1FZU3VMQWlISVgyUEwiLCJtYWMiOiJjZTYyNGE0Y2QxODBjODVlYjgxNmFkOGY2YzdiZGFhOTY4NTQyMDcxYjJiZjNkNjFhNzczNjQxY2MwYmQ2ZmRhIiwidGFnIjoiIn0%3D; expires=Sat, 31-May-2025 01:43:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">deaseapp_session=eyJpdiI6IkRrd3EvemlIaTEvWWQ3dzJnZlZtVEE9PSIsInZhbHVlIjoiSXAvQ3pCQjBvcitXa1dyQXhsUTFoWTdWZkU0elVFUmk3dFdGRUdCMkI5amI3TTgzc0VsSzRnaFdoS3M0NUhnTFRCdElCZVFUbFJlUTlKTzlhQkthcnNxYnpVR2tZR2xyTFpSRE9nZSsrUTRKVWNQa2pPVWk5MEVkN3RRZ3V1T2kiLCJtYWMiOiJhZTVlY2M4OWUwOTMzOTkwMmVjNzE1YjAxZDhkMzkyNDYyNDI5Yzk0MDdjNDVmN2M5MmIzNjdhMTM2YjUyOTNiIiwidGFnIjoiIn0%3D; expires=Sat, 31-May-2025 01:43:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2108678332\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1317424205 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">muRTgZWLZp8TXpCPOqELZrYZSwk3sM8lNAZ03Fak</span>\"\n  \"<span class=sf-dump-key>landing_site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"28 characters\">http://localhost/Admin-panel</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1317424205\", {\"maxDepth\":0})</script>\n"}}