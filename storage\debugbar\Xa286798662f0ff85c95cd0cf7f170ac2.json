{"__meta": {"id": "Xa286798662f0ff85c95cd0cf7f170ac2", "datetime": "2025-05-31 00:36:20", "utime": **********.371291, "method": "GET", "uri": "/Admin-panel/test-path", "ip": "::1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748648177.169658, "end": **********.371311, "duration": 3.201653003692627, "duration_str": "3.2s", "measures": [{"label": "Booting", "start": 1748648177.169658, "relative_start": 0, "end": **********.498829, "relative_end": **********.498829, "duration": 2.3291709423065186, "duration_str": "2.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.498845, "relative_start": 2.****************, "end": **********.371314, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "872ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET test-path", "middleware": "web", "uses": "Closure() {#1404\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#1371 …}\n  file: \"C:\\xampp\\htdocs\\Admin-panel\\routes\\web.php\"\n  line: \"185 to 207\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Froutes%2Fweb.php&line=185\" onclick=\"\">routes/web.php:185-207</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.10683, "accumulated_duration_str": "107ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `business_settings` where `key` = 'system_language' limit 1", "type": "query", "params": [], "bindings": ["system_language"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "middleware", "name": "localization", "file": "C:\\xampp\\htdocs\\Admin-panel\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\xampp\\htdocs\\Admin-panel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}], "start": **********.5486252, "duration": 0.10683, "duration_str": "107ms", "memory": 0, "memory_str": null, "filename": "localization:24", "source": "middleware::localization:24", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FHttp%2FMiddleware%2FLocalization.php&line=24", "ajax": false, "filename": "Localization.php", "line": "24"}, "connection": "dease_test", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\BusinessSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FAdmin-panel%2Fapp%2FModels%2FBusinessSetting.php&line=1", "ajax": false, "filename": "BusinessSetting.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "muRTgZWLZp8TXpCPOqELZrYZSwk3sM8lNAZ03Fak", "landing_site_direction": "ltr", "_previous": "array:1 [\n  \"url\" => \"http://localhost/Admin-panel/test-path\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/test-path", "status_code": "<pre class=sf-dump id=sf-dump-2041806602 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2041806602\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-773297128 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-773297128\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1576124289 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1576124289\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-63779242 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">en-US,en;q=0.9,fr-DZ;q=0.8,fr;q=0.7,ar-DZ;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1274 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5jRlo5KzllMDgvcERoQklrT3VUYkE9PSIsInZhbHVlIjoiVVFoUkF1aUFCQ1pIQjcxVWRKN0Y0bnJabkgrWFEwckhLVmF1NnQ3YkpPblJZVmhCL05ZK2h4c3Q3VGZVeWErRkNGeDJDY0RFMCt6NnM1d1E4cVlxRUpKN3lCUVFheWxUaXlCVWdpb21pRThQbmRyU3E0MEpvQlVoYVo3a25SeDNnaGlZR3B6YTJFc3J0KytQRlpHSE1CakpzbGR2OXRPeTN6ZmNUWmI4N2xnUlZCRW1LeHlPWElHcmthb3NkSnJ0R2NqUlUwTWZseW1zVzgrcTg3WEpzajFpWGdUMFZQMGd0eGN6ZlFDUWFBbz0iLCJtYWMiOiI1MjUzMzQ0OGQ1NTE1NTBkNzI5ZjQwZWE4OTFiNDJiNTM2ZjBiYjg4NTkxNDBhZjI4MjEwZmIwMzg3NzQ2ZWNhIiwidGFnIjoiIn0%3D; SL_G_WPT_TO=fr; XSRF-TOKEN=eyJpdiI6IkNmZzlKanFEK3hBSmszLzUwdkVpSkE9PSIsInZhbHVlIjoiYnRFbHhxSEw5Ni8wYnRXbFlvOHNpSGljRHZ4cUdkUWFoQXlOUzB1NDlSdG82MEkxbFRoVFEwR3hodDlZd2FodmhtdlF3a0k1K0cybUhuUUNLZU1OYW56b1AxWTdjbkZVS3BMNkIvdGtMUmEvTWtwa2JYMGJnMWJnMUt2THQzQzEiLCJtYWMiOiI4ZWYxOWYwNzlhN2U5ZjNhMmZkZWRhODk3ZDI1ZTdjY2E5NzgzZjdiYjBkYzVhOGRhNDNlZjc4ZDg0ZTA4MmE4IiwidGFnIjoiIn0%3D; deaseapp_session=eyJpdiI6Im9lNTdYUlpPQXRoUjVzRXBKODBoelE9PSIsInZhbHVlIjoiTE42enUvYU1zcEFaMngwNjU4RTZ1bW9MbXVSZFY2N25QTjRPUEtRT09IREdJVlRnZGhSTTFHSWNpWjgyT2NjeS9jZjVzRmdyTnB0ZStld2NDSlZQQ1orNVBXOHRwOE9zVmxYNE9wNXdYY2ZtSXBJQkpEcU5NcE1UeDUra1pCcmsiLCJtYWMiOiI3MmYyMGVlMzQ5ZmE1YWRmODk4YzJkYTE4ODI4ZDY2NmMyODEyMTJmZmYzZjY3ZTIyNzVjOTFkMzNlOWMxMDU3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-63779242\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1465730255 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>SL_G_WPT_TO</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">muRTgZWLZp8TXpCPOqELZrYZSwk3sM8lNAZ03Fak</span>\"\n  \"<span class=sf-dump-key>deaseapp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kBiAZ2CE1aEICB91UULNOk2vJpwX5OXi3AQPOsTY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465730255\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-733566875 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 30 May 2025 23:36:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"414 characters\">XSRF-TOKEN=eyJpdiI6ImYyLy85UWQ2K09QWUFtbkxjQXZCSVE9PSIsInZhbHVlIjoieEVQVWlPMTNRZis0WUxoNTZsZkJHcEcyZVNQbU9wQXczR3NhTjVQbFFMZVV1ckppZFllZHl5c05Fbk9hem9rUzFqUlFrMDMxa0s1WXVZVWhDOE01VEpUVitwN1k4bTUwMGJzcmMzMW4yTUNjSkF3YUZxajVrYThnUkdMRFRXaE0iLCJtYWMiOiI0Y2M2ZjY3ZGQ2YmQxOGU1Yzc3ZjMxNDc1ZGVhOTFmY2VkMTVlZGViMTUwYjRhYzE2OTYwZTQyMTY5NGZlMDRhIiwidGFnIjoiIn0%3D; expires=Sat, 31 May 2025 01:36:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"430 characters\">deaseapp_session=eyJpdiI6Ijk3T2hhTDc5NVZhSFRpRVpsUlZLUFE9PSIsInZhbHVlIjoiSEZ6eVlMaWYzcG9lNmFaV1hPa1ZrN3I0eDF5VkkwMXdyOWo5bjB4ZVJxTWt3WU92cjlQQVcxalAwbkVaTk1EanU5anA2dGcvY0lwNEVSaFowbjl5VFpHcHVQbHA5Rk8wMWZ4OGNzbXNjQ1ZKbFJ0bHJ5QlRPQVJpS0gxazRCZEMiLCJtYWMiOiJiZTkzNGJkMDhkNDI4NTVjMzEzMDIzN2VlMTU0MjFhOTY2NzMwYTQ3MTViMGUxYzJlMzY2YmNkYmYwNzk5YjQxIiwidGFnIjoiIn0%3D; expires=Sat, 31 May 2025 01:36:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImYyLy85UWQ2K09QWUFtbkxjQXZCSVE9PSIsInZhbHVlIjoieEVQVWlPMTNRZis0WUxoNTZsZkJHcEcyZVNQbU9wQXczR3NhTjVQbFFMZVV1ckppZFllZHl5c05Fbk9hem9rUzFqUlFrMDMxa0s1WXVZVWhDOE01VEpUVitwN1k4bTUwMGJzcmMzMW4yTUNjSkF3YUZxajVrYThnUkdMRFRXaE0iLCJtYWMiOiI0Y2M2ZjY3ZGQ2YmQxOGU1Yzc3ZjMxNDc1ZGVhOTFmY2VkMTVlZGViMTUwYjRhYzE2OTYwZTQyMTY5NGZlMDRhIiwidGFnIjoiIn0%3D; expires=Sat, 31-May-2025 01:36:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"416 characters\">deaseapp_session=eyJpdiI6Ijk3T2hhTDc5NVZhSFRpRVpsUlZLUFE9PSIsInZhbHVlIjoiSEZ6eVlMaWYzcG9lNmFaV1hPa1ZrN3I0eDF5VkkwMXdyOWo5bjB4ZVJxTWt3WU92cjlQQVcxalAwbkVaTk1EanU5anA2dGcvY0lwNEVSaFowbjl5VFpHcHVQbHA5Rk8wMWZ4OGNzbXNjQ1ZKbFJ0bHJ5QlRPQVJpS0gxazRCZEMiLCJtYWMiOiJiZTkzNGJkMDhkNDI4NTVjMzEzMDIzN2VlMTU0MjFhOTY2NzMwYTQ3MTViMGUxYzJlMzY2YmNkYmYwNzk5YjQxIiwidGFnIjoiIn0%3D; expires=Sat, 31-May-2025 01:36:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733566875\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1508048442 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">muRTgZWLZp8TXpCPOqELZrYZSwk3sM8lNAZ03Fak</span>\"\n  \"<span class=sf-dump-key>landing_site_direction</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ltr</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">http://localhost/Admin-panel/test-path</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508048442\", {\"maxDepth\":0})</script>\n"}}