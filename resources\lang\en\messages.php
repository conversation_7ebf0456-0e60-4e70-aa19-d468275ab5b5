<?php return array (
  'attributes' => 'Attributes',
  'add_new_attribute' => 'Add new attribute',
  'default' => 'Default',
  'name' => 'Name',
  'ex_:_new_attribute' => 'Ex : new attribute',
  'reset' => 'Reset',
  'submit' => 'Submit',
  'attribute_list' => 'Attribute list',
  'ex_:_attribute_name' => 'Ex : attribute name',
  'export' => 'Export',
  'download' => 'Download',
  'options' => 'Options',
  'excel' => 'Excel',
  'csv' => 'Csv',
  'sl' => 'Sl',
  'action' => 'Action',
  'edit' => 'Edit',
  'Want to delete this attribute ?' => 'Want to delete this attribute ?',
  'delete' => 'Delete',
  'Users' => 'Users',
  'Transactions & Reports' => 'Transactions & Reports',
  'Settings' => 'Settings',
  'Monitor your business general settings from here' => 'Monitor your business general settings from here',
  'System Module Setup' => 'System Module Setup',
  'Zone Setup' => 'Zone Setup',
  'Business Settings' => 'Business Settings',
  '3rd Party' => '3rd Party',
  'Social Media and Page Setup' => 'Social Media and Page Setup',
  'View All' => 'View All',
  'Dispatch Management' => 'Dispatch Management',
  'Modules Section' => 'Modules Section',
  'Select Module & Monitor your business module wise' => 'Select Module & Monitor your business module wise',
  'add_new_module' => 'Add new module',
  'Turotial' => 'Turotial',
  'Tour' => 'Tour',
  'Search Menu...' => 'Search Menu...',
  'dashboard' => 'Dashboard',
  'employee_handle' => 'Employee handle',
  'pos section' => 'Pos section',
  'New Sale' => 'New Sale',
  'order_management' => 'Order management',
  'orders' => 'Orders',
  'all' => 'All',
  'scheduled_orders' => 'Scheduled orders',
  'scheduled' => 'Scheduled',
  'pending' => 'Pending',
  'accepted_orders' => 'Accepted orders',
  'accepted' => 'Accepted',
  'processing_orders' => 'Processing orders',
  'processing' => 'Processing',
  'order_on_the_way' => 'Order on the way',
  'delivered_orders' => 'Delivered orders',
  'delivered' => 'Delivered',
  'canceled_orders' => 'Canceled orders',
  'canceled' => 'Canceled',
  'payment_failed_orders' => 'Payment failed orders',
  'payment_failed' => 'Payment failed',
  'refunded_orders' => 'Refunded orders',
  'refunded' => 'Refunded',
  'Order Refunds' => 'Order Refunds',
  'Refund Requests' => 'Refund Requests',
  'Promotion Management' => 'Promotion Management',
  'campaigns' => 'Campaigns',
  'basic_campaigns' => 'Basic campaigns',
  'item' => 'Item',
  'item_campaigns' => 'Item campaigns',
  'banners' => 'Banners',
  'coupons' => 'Coupons',
  'push' => 'Push',
  'notification' => 'Notification',
  'section' => 'Section',
  'product_management' => 'Product management',
  'categories' => 'Categories',
  'category' => 'Category',
  'sub_category' => 'Sub category',
  'bulk_import' => 'Bulk import',
  'bulk_export' => 'Bulk export',
  'units' => 'Units',
  'Product Setup' => 'Product Setup',
  'add' => 'Add',
  'new' => 'New',
  'add_new' => 'Add new',
  'list' => 'List',
  'review' => 'Review',
  'store' => 'Store',
  'management' => 'Management',
  'requests' => 'Requests',
  'new_stores' => 'New stores',
  'stores' => 'Stores',
  'settings' => 'Settings',
  'logout_warning_message' => 'Logout warning message',
  'yes' => 'Yes',
  'Don`t_Logout' => 'Don`t Logout',
  'sign_out' => 'Sign out',
  'business_setup' => 'Business setup',
  'profile' => 'Profile',
  'home' => 'Home',
  'software_version' => 'Software version',
  'You have new order, Check Please.' => 'You have new order  Check Please.',
  'Ok, let me check' => 'Ok  let me check',
  'Ok' => 'Ok',
  'Cancel' => 'Cancel',
  'are_you_sure' => 'Are you sure ?',
  'no' => 'No',
  'Yes' => 'Yes',
  'Are you sure?' => 'Are you sure ?',
  'text_copied' => 'Text copied',
  'Update option is disabled for demo!' => 'Update option is disabled for demo!',
  'error' => 'Error',
  '404_warning_message' => '404 warning message',
  'support' => 'Support',
  'login' => 'Login',
  'Your' => 'Your',
  'All Service' => 'All Service',
  'in one field' => 'In one field',
  'admin' => 'Admin',
  'signin' => 'Signin',
  'welcome_back_login_to_your_panel' => 'Welcome back login to your panel',
  'your_email' => 'Your email',
  'Please_enter_a_valid_email_address.' => 'Please enter a valid email address.',
  'password' => 'Password',
  'password_length_placeholder' => ':length characters required',
  'invalid_password_warning' => 'Invalid password warning',
  'remember_me' => 'Remember me',
  'Forget Password' => 'Forget Password',
  'Enter recaptcha value' => 'Enter recaptcha value',
  'Send_Mail_to_Your_Email' => 'Send Mail to Your Email',
  'A mail will be send to your registered email with a  link to change passowrd' => 'A mail will be send to your registered email with a  link to change passowrd',
  'Send Mail' => 'Send Mail',
  'plesae_enter_your_registerd_email' => 'Plesae enter your registerd email',
  'A mail has been sent to your registered email' => 'A mail has been sent to your registered email',
  'Click the link in the mail description to change password' => 'Click the link in the mail description to change password',
  'Credentials does not match.' => 'Credentials does not match.',
  'Jan' => 'Jan',
  'Feb' => 'Feb',
  'Mar' => 'Mar',
  'Apr' => 'Apr',
  'May' => 'May',
  'Jun' => 'Jun',
  'Jul' => 'Jul',
  'Aug' => 'Aug',
  'Sep' => 'Sep',
  'Oct' => 'Oct',
  'Nov' => 'Nov',
  'Dec' => 'Dec',
  'Sun' => 'Sun',
  'Mon' => 'Mon',
  'Tue' => 'Tue',
  'Wed' => 'Wed',
  'Thu' => 'Thu',
  'Fri' => 'Fri',
  'Sat' => 'Sat',
  'Grocery' => 'Grocery',
  'Dashboard' => 'Dashboard',
  'Hello, Here You Can Manage Your' => 'Hello  Here You Can Manage Your',
  'orders by Zone.' => 'Orders by Zone.',
  'All_Zones' => 'All Zones',
  'This_Year' => 'This Year',
  'This_Month' => 'This Month',
  'This_Week' => 'This Week',
  'items' => 'Items',
  'newly added' => 'Newly added',
  'Grocery Stores' => 'Grocery Stores',
  'customers' => 'Customers',
  'unassigned_orders' => 'Unassigned orders',
  'Accepted by Delivery Man' => 'Accepted by Delivery Man',
  'Packaging' => 'Packaging',
  'Out for Delivery' => 'Out for Delivery',
  'Gross Sale' => 'Gross Sale',
  'sale' => 'Sale',
  'This year' => 'This year',
  'This month' => 'This month',
  'This week' => 'This week',
  'User Statistics' => 'User Statistics',
  'Overall' => 'Overall',
  'total_users' => 'Total users',
  'customer' => 'Customer',
  'delivery_man' => 'Delivery man',
  'top selling stores' => 'Top selling stores',
  'view_all' => 'View all',
  'order : ' => 'Order :',
  'most rated' => 'Most rated',
  'top selling' => 'Top selling',
  'sold' => 'Sold',
  'top rated' => 'Top rated',
  'top_deliveryman' => 'Top deliveryman',
  'top_customers' => 'Top customers',
  'Orders' => 'Orders',
  'Customer' => 'Customer',
  'Store' => 'Store',
  'Delivery man' => 'Delivery man',
  'Admin Comission' => 'Admin Comission',
  'Delivery Comission' => 'Delivery Comission',
  'all_orders' => 'All orders',
  'pending_orders' => 'Pending orders',
  'push_notification' => 'Push notification',
  'item_section' => 'Item section',
  'item_list' => 'Item list',
  'review_list' => 'Review list',
  'store_section' => 'Store section',
  'store_management' => 'Store management',
  'pending_requests' => 'Pending requests',
  'add_store' => 'Add store',
  'stores_list' => 'Stores list',
  'POS Orders' => 'POS Orders',
  'product_section' => 'Product section',
  'select_store' => 'Select store',
  'select_category' => 'Select category',
  'all_categories' => 'All categories',
  'ex_:_search_here' => 'Ex : search here',
  'search_here' => 'Search here',
  'no_products_on_pos_search' => 'No products on pos search',
  'billing_section' => 'Billing section',
  'select_customer' => 'Select customer',
  'Add new customer' => 'Add new customer',
  'Delivery Infomation' => 'Delivery Infomation',
  'Home Delivery' => 'Home Delivery',
  'qty' => 'Qty',
  'price' => 'Price',
  'addon' => 'Addon',
  'subtotal' => 'Subtotal',
  'discount' => 'Discount',
  'delivery_fee' => 'Delivery fee',
  'tax' => 'Tax',
  'total' => 'Total',
  'Payment Method' => 'Payment Method',
  'Cash On Delivery' => 'Cash On Delivery',
  'Wallet' => 'Wallet',
  'place_order' => 'Place order',
  'Clear Cart' => 'Clear Cart',
  'update_discount' => 'Update discount',
  'type' => 'Type',
  'amount' => 'Amount',
  'percent' => 'Percent',
  'update_tax' => 'Update tax',
  'delivery_options' => 'Delivery options',
  'please_select_a_store_first' => 'Please select a store first',
  'print_invoice' => 'Print invoice',
  'Proceed, If thermal printer is ready.' => 'Proceed  If thermal printer is ready.',
  'back' => 'Back',
  'add_new_customer' => 'Add new customer',
  'first_name' => 'First name',
  'last_name' => 'Last name',
  'email' => 'Email',
  'Ex_:<EMAIL>' => 'Ex : <EMAIL>',
  'phone' => 'Phone',
  'with_country_code' => 'With country code',
  'out_of_coverage' => 'Out of coverage',
  'The Geolocation service failed' => 'The Geolocation service failed',
  'Your browser doesn`t support geolocation' => 'Your browser doesn`t support geolocation',
  'product_already_added_in_cart' => 'Product already added in cart',
  'product_has_been_updated_in_cart' => 'Product has been updated in cart',
  'Sorry, product out of stock' => 'Sorry  product out of stock',
  'Sorry, you can not add multiple stores data in same cart' => 'Sorry  you can not add multiple stores data in same cart',
  'product_has_been_added_in_cart' => 'Product has been added in cart',
  'item_has_been_removed_from_cart' => 'Item has been removed from cart',
  'Sorry, the minimum value was reached' => 'Sorry  the minimum value was reached',
  'Campaign List' => 'Campaign List',
  'campaign' => 'Campaign',
  'add_new_campaign' => 'Add new campaign',
  'campaign_list' => 'Campaign list',
  'Ex:_Search Title ...' => 'Ex: Search Title ...',
  'download_options' => 'Download options',
  '#' => '#',
  'title' => 'Title',
  'module' => 'Module',
  'date_duration' => 'Date duration',
  'time_duration' => 'Time duration',
  'status' => 'Status',
  'edit_campaign' => 'Edit campaign',
  'Want_to_delete_this_item' => 'Want to delete this item',
  'delete_campaign' => 'Delete campaign',
  'Ex:_Campaign title...' => 'Ex: Campaign title...',
  'date' => 'Date',
  'time' => 'Time',
  'Want to delete this item ?' => 'Want to delete this item ?',
  'banner' => 'Banner',
  'add_new_banner' => 'Add new banner',
  'new_banner' => 'New banner',
  'zone' => 'Zone',
  'select' => 'Select',
  'banner_type' => 'Banner type',
  'store_wise' => 'Store wise',
  'item_wise' => 'Item wise',
  'select_item' => 'Select item',
  'default_link' => 'Default link',
  'optional' => 'Optional',
  'campaign_image' => 'Campaign image',
  'ratio' => 'Ratio',
  'choose_file' => 'Choose file',
  'banner_list' => 'Banner list',
  'search_by_title' => 'Search by title',
  'SL' => 'SL',
  'featured' => 'Featured',
  'edit_banner' => 'Edit banner',
  'Want to delete this banner ?' => 'Want to delete this banner ?',
  'delete_banner' => 'Delete banner',
  'banner_added_successfully' => 'Banner added successfully',
  'Add new coupon' => 'Add new coupon',
  'new_coupon' => 'New coupon',
  'coupon_type' => 'Coupon type',
  'zone_wise' => 'Zone wise',
  'free_delivery' => 'Free delivery',
  'first_order' => 'First order',
  'select_zone' => 'Select zone',
  'code' => 'Code',
  'limit_for_same_user' => 'Limit for same user',
  'start_date' => 'Start date',
  'expire_date' => 'Expire date',
  'discount_type' => 'Discount type',
  'Currently you need to manage discount with the Restaurant.' => 'Currently you need to manage discount with the Restaurant.',
  'max_discount' => 'Max discount',
  'min_purchase' => 'Min purchase',
  'coupon_list' => 'Coupon list',
  'Ex:_Coupon Title' => 'Ex: Coupon Title',
  'total_uses' => 'Total uses',
  'edit_coupon' => 'Edit coupon',
  'Want to delete this coupon ?' => 'Want to delete this coupon ?',
  'delete_coupon' => 'Delete coupon',
  'new_notification' => 'New notification',
  'send_to' => 'Send to',
  'select_tergat' => 'Select tergat',
  'deliveryman' => 'Deliveryman',
  'description' => 'Description',
  'image' => 'Image',
  'send_notification' => 'Send notification',
  'Notification list' => 'Notification list',
  'search_notification' => 'Search notification',
  'tergat' => 'Tergat',
  'edit_notification' => 'Edit notification',
  'Want to delete this notification ?' => 'Want to delete this notification ?',
  'delete_notification' => 'Delete notification',
  'No Image' => 'No Image',
  'you want to sent notification to' => 'You want to sent notification to',
  'send' => 'Send',
  'add_new_unit' => 'Add new unit',
  'unit_name' => 'Unit name',
  'unit_list' => 'Unit list',
  'search_unit' => 'Search unit',
  'unit' => 'Unit',
  'Want to delete this unit ?' => 'Want to delete this unit ?',
  'new_joining_requests' => 'New joining requests',
  'pending_stores' => 'Pending stores',
  'denied_stores' => 'Denied stores',
  'ex_:_Search_Store_Name' => 'Ex : Search Store Name',
  'search' => 'Search',
  'store_information' => 'Store information',
  'owner_information' => 'Owner information',
  'id' => 'Id',
  'approve' => 'Approve',
  'you_want_to_approve_this_application' => 'You want to approve this application',
  'deny' => 'Deny',
  'you_want_to_deny_this_application' => 'You want to deny this application',
  'add_store_name' => 'Add store name',
  'add_new_store' => 'Add new store',
  'Default' => 'Default',
  'store_name' => 'Store name',
  'address' => 'Address',
  'Store Logo & Covers' => 'Store Logo & Covers',
  'logo' => 'Logo',
  '1:1' => '1:1',
  'Store Cover' => 'Store Cover',
  '2:1' => '2:1',
  'vat/tax' => 'Vat/tax',
  'Estimated Delivery Time ( Min & Maximum Time)' => 'Estimated Delivery Time ( Min & Maximum Time)',
  'Minimum Time' => 'Minimum Time',
  'Ex :' => 'Ex :',
  'Maximum Time' => 'Maximum Time',
  'minutes' => 'Minutes',
  'hours' => 'Hours',
  'days' => 'Days',
  'done' => 'Done',
  'select_zone_for_map' => 'Select zone for map',
  'latitude' => 'Latitude',
  'store_lat_lng_warning' => 'Store lat lng warning',
  'Ex:' => 'Ex:',
  'longitude' => 'Longitude',
  'search_your_location_here' => 'Search your location here',
  'account_information' => 'Account information',
  'Must_contain_at_least_one_number_and_one_uppercase_and_lowercase_letter_and_symbol,_and_at_least_8_or_more_characters' => 'Must contain at least one number and one uppercase and lowercase letter and symbol  and at least 8 or more characters',
  'confirm_password' => 'Confirm password',
  'please_only_input_png_or_jpg_type_file' => 'Please only input png or jpg type file',
  'file_size_too_big' => 'File size too big',
  'Store List' => 'Store List',
  'total_stores' => 'Total stores',
  'active_stores' => 'Active stores',
  'inactive_stores' => 'Inactive stores',
  'newly_joined_stores' => 'Newly joined stores',
  'total_transactions' => 'Total transactions',
  'commission_earned' => 'Commission earned',
  'total_store_withdraws' => 'Total store withdraws',
  'you_want_to_change_this_store_status' => 'You want to change this store status',
  'view' => 'View',
  'edit_store' => 'Edit store',
  'You want to remove this store' => 'You want to remove this store',
  'delete_store' => 'Delete store',
  'Store Bulk Import' => 'Store Bulk Import',
  'stores_bulk_import' => 'Stores bulk import',
  'STEP 1' => 'STEP 1',
  'Download Excel File' => 'Download Excel File',
  'STEP 2' => 'STEP 2',
  'Match Spread sheet data according to instruction' => 'Match Spread sheet data according to instruction',
  'STEP 3' => 'STEP 3',
  'Validate data and complete import' => 'Validate data and complete import',
  'Instructions' => 'Instructions',
  '1. Download the format file and fill it with proper data.' => '1. Download the format file and fill it with proper data.',
  '2. You can download the example file to understand how the data must be filled.' => '2. You can download the example file to understand how the data must be filled.',
  '4. After uploading stores you need to edit them and set stores`s logo and cover.' => '4. After uploading stores you need to edit them and set stores`s logo and cover.',
  '5. You can get module id and  zone id from their list, please input the right ids.' => '5. You can get module id and  zone id from their list  please input the right ids.',
  '6. For delivery time the format is "from-to type" for example: "30-40 min". Also you can use days or hours as type. Please be carefull about this format or leave this field empty.' => '6. For delivery time the format is  from-to type  for example:  30-40 min . Also you can use days or hours as type. Please be carefull about this format or leave this field empty.',
  '7. You can upload your store images in store folder from gallery, and copy image`s path.' => '7. You can upload your store images in store folder from gallery  and copy image`s path.',
  '8. Default password for store is 12345678.' => '8. Default password for store is 12345678.',
  '9. Latitude must be a number between -90 to 90 and Longitude must a number between -180 to 180. Otherwise it will create server error' => '9. Latitude must be a number between -90 to 90 and Longitude must a number between -180 to 180. Otherwise it will create server error',
  'Download Spreadsheet Template' => 'Download Spreadsheet Template',
  'Template with Existing Data' => 'Template with Existing Data',
  'Template without Data' => 'Template without Data',
  'Import Stores File' => 'Import Stores File',
  'Choose File' => 'Choose File',
  'Clear' => 'Clear',
  'update' => 'Update',
  'Import' => 'Import',
  'You_want_to_' => 'You want to ',
  'Cancelled' => 'Cancelled',
  'restaurant_bulk_export' => 'Restaurant bulk export',
  'export_stores' => 'Export stores',
  'Select Data Type' => 'Select Data Type',
  'Select Data Range and Export' => 'Select Data Range and Export',
  'select_type' => 'Select type',
  'all_data' => 'All data',
  'date_wise' => 'Date wise',
  'id_wise' => 'Id wise',
  'start_id' => 'Start id',
  'from_date' => 'From date',
  'end_id' => 'End id',
  'to_date' => 'To date',
  'clear' => 'Clear',
  'User Overview' => 'User Overview',
  'Hello,_here_you_can_manage_your_users_by_zone.' => 'Hello  here you can manage your users by zone.',
  'total_customer' => 'Total customer',
  'total_delivery_man' => 'Total delivery man',
  'total_employee' => 'Total employee',
  'Customer Statistics' => 'Customer Statistics',
  'active_customer' => 'Active customer',
  'newly_joined' => 'Newly joined',
  'blocked_customer' => 'Blocked customer',
  'customer_growth' => 'Customer growth',
  'this_year' => 'This year',
  'customer_satisfaction' => 'Customer satisfaction',
  'review_received' => 'Review received',
  'Positive' => 'Positive',
  'Good' => 'Good',
  'Neutral' => 'Neutral',
  'Negetive' => 'Negetive',
  'Deliveryman Statistics' => 'Deliveryman Statistics',
  'active_delivery_man' => 'Active delivery man',
  'newly_joined_delivery_man' => 'Newly joined delivery man',
  'inactive_deliveryman' => 'Inactive deliveryman',
  'Currently Active Delivery Men' => 'Currently Active Delivery Men',
  'Search Delivery Man ...' => 'Search Delivery Man ...',
  'View All Delivery Men' => 'View All Delivery Men',
  'New_Customer_Growth' => 'New Customer Growth',
  'modules' => 'Modules',
  'deliveryman_section' => 'Deliveryman section',
  'deliveryman_management' => 'Deliveryman management',
  'vehicles_category' => 'Vehicles category',
  'add_delivery_man' => 'Add delivery man',
  'new_delivery_man' => 'New delivery man',
  'deliveryman_list' => 'Deliveryman list',
  'reviews' => 'Reviews',
  'customer_section' => 'Customer section',
  'customer_management' => 'Customer management',
  'customer_wallet' => 'Customer wallet',
  'add_fund' => 'Add fund',
  'report' => 'Report',
  'bonus' => 'Bonus',
  'customer_loyalty_point' => 'Customer loyalty point',
  'subscribed_emails' => 'Subscribed emails',
  'subscribed_mail_list' => 'Subscribed mail list',
  'contact_messages' => 'Contact messages',
  'employee' => 'Employee',
  'employee_Role' => 'Employee Role',
  'Employee' => 'Employee',
  'employees' => 'Employees',
  'add_new_Employee' => 'Add new Employee',
  'Employee_list' => 'Employee list',
  'Vehicle_List' => 'Vehicle List',
  'vehicles_category_list' => 'Vehicles category list',
  'add_vehicle_category' => 'Add vehicle category',
  'Ex_:_Search_by_type...' => 'Ex : Search by type...',
  'Type' => 'Type',
  'minimum_coverage_area' => 'Minimum coverage area',
  'km' => 'Km',
  'Maximum_coverage_area' => 'Maximum coverage area',
  'Extra_charges' => 'Extra charges',
  'edit_vehicle' => 'Edit vehicle',
  'delete_vehicle' => 'Delete vehicle',
  'Add new delivery-man' => 'Add new delivery-man',
  'add_new_deliveryman' => 'Add new deliveryman',
  'general_information' => 'General information',
  'deliveryman_type' => 'Deliveryman type',
  'freelancer' => 'Freelancer',
  'salary_based' => 'Salary based',
  'Vehicle' => 'Vehicle',
  'select_vehicle' => 'Select vehicle',
  'deliveryman_image' => 'Deliveryman image',
  'identity_type' => 'Identity type',
  'passport' => 'Passport',
  'driving_license' => 'Driving license',
  'nid' => 'Nid',
  'store_id' => 'Store id',
  'identity_number' => 'Identity number',
  'identity_image' => 'Identity image',
  'login_information' => 'Login information',
  'pending_delivery_man' => 'Pending delivery man',
  'denied_deliveryman' => 'Denied deliveryman',
  'ex_: search_delivery_man' => 'Ex : search delivery man',
  'contact_info' => 'Contact info',
  'total_orders' => 'Total orders',
  'availability_status' => 'Availability status',
  'currently_assigned_orders' => 'Currently assigned orders',
  'active_status' => 'Active status',
  'Delivery Man Preview' => 'Delivery Man Preview',
  'deliveryman_preview' => 'Deliveryman preview',
  'want_to_enable_earnings' => 'Want to enable earnings',
  'want_to_disable_earnings' => 'Want to disable earnings',
  'Vehicle Information' => 'Vehicle Information',
  'Vehicle_Type' => 'Vehicle Type',
  'Vehicle_Extra_Charges' => 'Vehicle Extra Charges',
  'Vehicle_minimum_coverage_area' => 'Vehicle minimum coverage area',
  'Vehicle_maximum_coverage_area' => 'Vehicle maximum coverage area',
  'excellent' => 'Excellent',
  'good' => 'Good',
  'average' => 'Average',
  'below_average' => 'Below average',
  'poor' => 'Poor',
  'Identity Documents' => 'Identity Documents',
  'Identity_Type' => 'Identity Type',
  'identification_number' => 'Identification number',
  'Identity_Image' => 'Identity Image',
  'Close' => 'Close',
  'reviewer' => 'Reviewer',
  'order_id' => 'Order id',
  'no_data_found' => 'No data found',
  'deliverymen' => 'Deliverymen',
  'ex_:_search_name' => 'Ex : search name',
  'online' => 'Online',
  'Want to remove this deliveryman ?' => 'Want to remove this deliveryman ?',
  'offline' => 'Offline',
  'info' => 'Info',
  'transaction' => 'Transaction',
  'conversations' => 'Conversations',
  'total_delivered_orders' => 'Total delivered orders',
  'cash_in_hand' => 'Cash in hand',
  'total_earning' => 'Total earning',
  'you_want_to_suspend_this_deliveryman' => 'You want to suspend this deliveryman',
  'suspend_this_delivery_man' => 'Suspend this delivery man',
  'Employee List' => 'Employee List',
  'Employee_table' => 'Employee table',
  'Role' => 'Role',
  'edit_Employee' => 'Edit Employee',
  'Want_to_delete_this_role' => 'Want to delete this role',
  'delete_Employee' => 'Delete Employee',
  'Employee Add' => 'Employee Add',
  'add_new_employee' => 'Add new employee',
  'select_Role' => 'Select Role',
  'Employee Edit' => 'Employee Edit',
  'Employee_update' => 'Employee update',
  'custom_role' => 'Custom role',
  'role_name' => 'Role name',
  'role_name_example' => 'Role name example',
  'module_permission' => 'Module permission',
  'select_all' => 'Select all',
  'collect_Cash' => 'Collect Cash',
  'attribute' => 'Attribute',
  'coupon' => 'Coupon',
  'provide_dm_earning' => 'Provide dm earning',
  'order' => 'Order',
  'withdraw_list' => 'Withdraw list',
  'parcel' => 'Parcel',
  'pos' => 'Pos',
  'roles_table' => 'Roles table',
  'ex_:_search_role_name' => 'Ex : search role name',
  'created_at' => 'Created at',
  'edit_role' => 'Edit role',
  'delete_role' => 'Delete role',
  'account' => 'Account',
  'provide dm earning' => 'Provide dm earning',
  'withdraw list' => 'Withdraw list',
  'customer management' => 'Customer management',
  'custom role' => 'Custom role',
  'Edit Role' => 'Edit Role',
  'Select All' => 'Select All',
  'collect_cash' => 'Collect cash',
  'Contact Messages' => 'Contact Messages',
  'all_message_lists' => 'All message lists',
  'message_lists' => 'Message lists',
  'ex_:_message_name' => 'Ex : message name',
  'subject' => 'Subject',
  'Seen/Unseen' => 'Seen/Unseen',
  'Subscribed Emails' => 'Subscribed Emails',
  'ex_:_search_email' => 'Ex : search email',
  'customer_loyalty_point_report' => 'Customer loyalty point report',
  'filter_options' => 'Filter options',
  'select_transaction_type' => 'Select transaction type',
  'point_to_wallet' => 'Point to wallet',
  'order_place' => 'Order place',
  'filter' => 'Filter',
  'summary' => 'Summary',
  'debit' => 'Debit',
  'credit' => 'Credit',
  'balance' => 'Balance',
  'transactions' => 'Transactions',
  'transaction_id' => 'Transaction id',
  'transaction_type' => 'Transaction type',
  'reference' => 'Reference',
  'not_found' => 'Not found',
  'you_want_to_add_fund' => 'You want to add fund',
  'to' => 'To',
  'to_wallet' => 'To wallet',
  'fund_added_successfully' => 'Fund added successfully',
  'customer_wallet_report' => 'Customer wallet report',
  'add_fund_by_admin' => 'Add fund by admin',
  'add_fund_by_customer' => 'Add fund by customer',
  'refund_order' => 'Refund order',
  'admin_bonus' => 'Admin bonus',
  'partial_payment' => 'Partial payment',
  'loyalty_point' => 'Loyalty point',
  'order_refund' => 'Order refund',
  'bonuses' => 'Bonuses',
  'wallet_bonus_setup' => 'Wallet bonus setup',
  'See_how_it_works!' => 'See how it works!',
  'Bonus_Title' => 'Bonus Title',
  'Ex:_EID_Dhamaka' => 'Ex: EID Dhamaka',
  'Short_Description' => 'Short Description',
  'Bonus_Type' => 'Bonus Type',
  'percentage' => 'Percentage',
  'Bonus_Amount' => 'Bonus Amount',
  'Set_the_bonus_amount/percentage_a_customer_will_receive_after_adding_money_to_his_wallet.' => 'Set the bonus amount/percentage a customer will receive after adding money to his wallet.',
  'Ex:_100' => 'Ex: 100',
  'Minimum_Add_Money_Amount' => 'Minimum Add Money Amount',
  'Set_the_minimum_add_money_amount_for_a_customer_to_be_eligible_for_the_bonus.' => 'Set the minimum add money amount for a customer to be eligible for the bonus.',
  'Ex:_10' => 'Ex: 10',
  'Maximum_Bonus' => 'Maximum Bonus',
  'Set_the_maximum_bonus_amount_a_customer_can_receive_for_adding_money_to_his_wallet.' => 'Set the maximum bonus amount a customer can receive for adding money to his wallet.',
  'Ex:_1000' => 'Ex: 1000',
  'bonus_list' => 'Bonus list',
  'Ex_:_Search_by_bonus_title' => 'Ex : Search by bonus title',
  'bonus_title' => 'Bonus title',
  'bonus_info' => 'Bonus info',
  'bonus_amount' => 'Bonus amount',
  'started_on' => 'Started on',
  'expires_on' => 'Expires on',
  'minimum_add_amount' => 'Minimum add amount',
  'maximum_bonus' => 'Maximum bonus',
  'edit_bonus' => 'Edit bonus',
  'Want to delete this bonus ?' => 'Want to delete this bonus ?',
  'delete_bonus' => 'Delete bonus',
  'Wallet_bonus_is_only_applicable_when_a_customer_add_fund_to_wallet_via_outside_payment_gateway_!' => 'Wallet bonus is only applicable when a customer add fund to wallet via outside payment gateway !',
  'Customer_will_get_extra_amount_to_his_/_her_wallet_additionally_with_the_amount_he_/_she_added_from_other_payment_gateways._The_bonus_amount_will_be_deduct_from_admin_wallet_&_will_consider_as_admin_expense.' => 'Customer will get extra amount to his / her wallet additionally with the amount he / she added from other payment gateways. The bonus amount will be deduct from admin wallet & will consider as admin expense.',
  'wallet_bonus_update' => 'Wallet bonus update',
  'select_date' => 'Select date',
  'Customer List' => 'Customer List',
  'search_by_name' => 'Search by name',
  'copy' => 'Copy',
  'print' => 'Print',
  'columns' => 'Columns',
  'contact_information' => 'Contact information',
  'total_order' => 'Total order',
  'active/Inactive' => 'Active/Inactive',
  'actions' => 'Actions',
  'active' => 'Active',
  'inactive' => 'Inactive',
  'you_want_to_block_this_customer' => 'You want to block this customer',
  'view_customer' => 'View customer',
  'Review List' => 'Review List',
  'deliveryman_reviews' => 'Deliveryman reviews',
  'ex_:_search_delivery_man' => 'Ex : search delivery man',
  'rating' => 'Rating',
  'conversation_list' => 'Conversation list',
  'view_conversation' => 'View conversation',
  'order_transactions' => 'Order transactions',
  'mm/dd/yyyy' => 'Mm/dd/yyyy',
  'delivery_fee_earned' => 'Delivery fee earned',
  'delivery_tips' => 'Delivery tips',
  'Order Details' => 'Order Details',
  'order_details' => 'Order details',
  'Previous order' => 'Previous order',
  'Next order' => 'Next order',
  'show_locations_on_map' => 'Show locations on map',
  'payment_method' => 'Payment method',
  'wallet' => 'Wallet',
  'reference_code' => 'Reference code',
  'Order Type' => 'Order Type',
  'delivery' => 'Delivery',
  'payment_status' => 'Payment status',
  'paid' => 'Paid',
  'cutlery' => 'Cutlery',
  'item_details' => 'Item details',
  'addons' => 'Addons',
  'Variation' => 'Variation',
  'items_price' => 'Items price',
  'addon_cost' => 'Addon cost',
  'coupon_discount' => 'Coupon discount',
  'delivery_man_tips' => 'Delivery man tips',
  'additional_charge' => 'Additional charge',
  'customer_information' => 'Customer information',
  'delivery_info' => 'Delivery info',
  'contact' => 'Contact',
  'Floor' => 'Floor',
  'Road' => 'Road',
  'House' => 'House',
  'order_proof' => 'Order proof',
  'add_Order Rejection_Note' => 'Add Order Rejection Note',
  'close' => 'Close',
  'Confirm_Order Rejection' => 'Confirm Order Rejection',
  'reference_code_add' => 'Reference code add',
  'add_order_proof' => 'Add order proof',
  'save_changes' => 'Save changes',
  'assign_deliveryman' => 'Assign deliveryman',
  'assign' => 'Assign',
  'location_data' => 'Location data',
  'order_updated_successfully' => 'Order updated successfully',
  'you_want_to_remove_this_order_item' => 'You want to remove this order item',
  'you_want_to_edit_this_order' => 'You want to edit this order',
  'you_want_to_cancel_editing' => 'You want to cancel editing',
  'you_want_to_submit_all_changes_for_this_order' => 'You want to submit all changes for this order',
  'Are you sure ?' => 'Are you sure ?',
  'Enter processing time' => 'Enter processing time',
  'Enter Processing time in minutes' => 'Enter Processing time in minutes',
  'No' => 'No',
  'Change status to canceled ?' => 'Change status to canceled ?',
  'food_campaigns' => 'Food campaigns',
  'food_management' => 'Food management',
  'addon_list' => 'Addon list',
  'Food Setup' => 'Food Setup',
  'food_list' => 'Food list',
  'restaurant_section' => 'Restaurant section',
  'restaurant_management' => 'Restaurant management',
  'new_restaurants' => 'New restaurants',
  'add new restaurant' => 'Add new restaurant',
  'restaurants_list' => 'Restaurants list',
  'restaurants' => 'Restaurants',
  'Proceed,_If_thermal_printer_is_ready.' => 'Proceed  If thermal printer is ready.',
  'cash_receipt' => 'Cash receipt',
  'contact_name' => 'Contact name',
  'desc' => 'Desc',
  'variation' => 'Variation',
  'delivery_charge' => 'Delivery charge',
  'Paid by' => 'Paid by',
  'change' => 'Change',
  'THANK YOU' => 'THANK YOU',
  'Customer Details' => 'Customer Details',
  'customer_id' => 'Customer id',
  'joined_at' => 'Joined at',
  'Previous_customer' => 'Previous customer',
  'Next_customer' => 'Next customer',
  'wallet_balance' => 'Wallet balance',
  'loyalty_point_balance' => 'Loyalty point balance',
  'order_list' => 'Order list',
  'ex_:_search_ID' => 'Ex : search ID',
  'total_amount' => 'Total amount',
  'invoice' => 'Invoice',
  'Update delivery-man' => 'Update delivery-man',
  'update_deliveryman' => 'Update deliveryman',
  'vehicle' => 'Vehicle',
  'identity_images' => 'Identity images',
  'update_identity_image' => 'Update identity image',
  'vehicle_view' => 'Vehicle view',
  'vehicle_type' => 'Vehicle type',
  'Total Orders' => 'Total Orders',
  'Currenty Assigned Orders' => 'Currenty Assigned Orders',
  'Active Status' => 'Active Status',
  'طعام' => 'طعام',
  'foods' => 'Foods',
  'Cooking' => 'Cooking',
  'profile_settings' => 'Profile settings',
  'nav_menu' => 'Nav menu',
  'basic_information' => 'Basic information',
  'full_name' => 'Full name',
  'Display_name' => 'Display name',
  'your_first_name' => 'Your first name',
  'your_last_name' => 'Your last name',
  'enter_new_email_address' => 'Enter new email address',
  'Want to update admin info ?' => 'Want to update admin info ?',
  'save' => 'Save',
  'change_your_password' => 'Change your password',
  'new_password' => 'New password',
  'want_to_update_admin_password' => 'Want to update admin password',
  'Shop' => 'Shop',
  'products' => 'Products',
  'contact_person_name' => 'Contact person name',
  'Contact Number' => 'Contact Number',
  '* pin the address in the map to calculate delivery fee' => '* pin the address in the map to calculate delivery fee',
  'Delivery fee' => 'Delivery fee',
  'Update_Delivery address' => 'Update Delivery address',
  'Order List' => 'Order List',
  'selected' => 'Selected',
  'current_page_only' => 'Current page only',
  'pdf' => 'Pdf',
  'order_status' => 'Order status',
  'order_date' => 'Order date',
  'order_filter' => 'Order filter',
  'order_type' => 'Order type',
  'take_away' => 'Take away',
  'date_between' => 'Date between',
  'Clear all filters' => 'Clear all filters',
  'partially_paid' => 'Partially paid',
  'confirmed' => 'Confirmed',
  'home Delivery' => 'Home Delivery',
  'unpaid' => 'Unpaid',
  'out_for_delivery' => 'Out for delivery',
  'failed' => 'Failed',
  'refundRequest' => 'RefundRequest',
  'Refund' => 'Refund',
  'requested' => 'Requested',
  'Rejected' => 'Rejected',
  'Update campaign' => 'Update campaign',
  'update_campaign' => 'Update campaign',
  'Item Info' => 'Item Info',
  'new_food' => 'New food',
  'short_description' => 'Short description',
  'Item Image' => 'Item Image',
  'item_image' => 'Item image',
  'Item Details' => 'Item Details',
  'select_module' => 'Select module',
  'total_stock' => 'Total stock',
  'maximum_cart_quantity' => 'Maximum cart quantity',
  'store_required_warning' => 'Make sure you have selected a store first!',
  'category_required_warning' => 'Make sure you have selected a category first!',
  'item_type' => 'Item type',
  'non_veg' => 'Non veg',
  'veg' => 'Veg',
  'Currently you need to manage discount with store.' => 'Currently you need to manage discount with store.',
  'food_variations' => 'Food variations',
  'add_new_variation' => 'Add new variation',
  'Add Attribute' => 'Add Attribute',
  'time_schedule' => 'Time schedule',
  'end_date' => 'End date',
  'start_time' => 'Start time',
  'end_time' => 'End time',
  'enter_choice_values' => 'Enter choice values',
  'Campaign updated successfully' => 'Campaign updated successfully',
  'add new variation' => 'Add new variation',
  'selcetion_type' => 'Selcetion type',
  'Multiple' => 'Multiple',
  'Single' => 'Single',
  'Min' => 'Min',
  'Max' => 'Max',
  'Required' => 'Required',
  'Delete' => 'Delete',
  'Option_name' => 'Option name',
  'Additional_price' => 'Additional price',
  'Add_New_Option' => 'Add New Option',
  'new_campaign' => 'New campaign',
  'daily_start_time' => 'Daily start time',
  'daily_end_time' => 'Daily end time',
  'Update Banner' => 'Update Banner',
  'update_banner' => 'Update banner',
  'banner_image' => 'Banner image',
  'banner_updated_successfully' => 'Banner updated successfully',
  'coupon_update' => 'Coupon update',
  'update_notification' => 'Update notification',
  'notification_update' => 'Notification update',
  'all_zone' => 'All zone',
  'send_again' => 'Send again',
  'Add new category' => 'Add new category',
  'add_new_category' => 'Add new category',
  'new_category' => 'New category',
  'category_list' => 'Category list',
  'search_categories' => 'Search categories',
  'ex_:_categories' => 'Ex : categories',
  'priority' => 'Priority',
  'normal' => 'Normal',
  'medium' => 'Medium',
  'high' => 'High',
  'edit_category' => 'Edit category',
  'Want to delete this category' => 'Want to delete this category',
  'delete_category' => 'Delete category',
  'Update category' => 'Update category',
  'category_update' => 'Category update',
  'Add new sub category' => 'Add new sub category',
  'add_new_sub_category' => 'Add new sub category',
  'new_sub_category' => 'New sub category',
  'main_category' => 'Main category',
  'Select Main Category' => 'Select Main Category',
  'sub_category_list' => 'Sub category list',
  'ex_:_search_sub_categories' => 'Ex : search sub categories',
  'ex_:_sub_categories' => 'Ex : sub categories',
  'sub' => 'Sub',
  'category_bulk_import' => 'Category bulk import',
  '3. Once you have downloaded and filled the format file, upload it in the form below and submit.' => '3. Once you have downloaded and filled the format file  upload it in the form below and submit.',
  '4. After uploading categories you need to edit them and set category`s images.' => '4. After uploading categories you need to edit them and set category`s images.',
  '5. For parent category "position" will 0 and for sub category it will be 1.' => '5. For parent category  position  will 0 and for sub category it will be 1.',
  '6. By default status will be 1, please input the right ids.' => '6. By default status will be 1  please input the right ids.',
  '7. For a category parent_id will be empty, for sub category it will be the category id.' => '7. For a category parent id will be empty  for sub category it will be the category id.',
  '8. For a sub category module id will it`s parents module id.' => '8. For a sub category module id will it`s parents module id.',
  'import_categories_file' => 'Import categories file',
  'Category Bulk Export' => 'Category Bulk Export',
  'export_categories' => 'Export categories',
  'Update Attribute' => 'Update Attribute',
  'attribute_update' => 'Attribute update',
  'updated_attribute' => 'Updated attribute',
  'add_new_item' => 'Add new item',
  'new_item' => 'New item',
  'item_thumbnail' => 'Item thumbnail',
  'please_select_store' => 'Please select store',
  'select_sub_category' => 'Select sub category',
  'Maximum_Purchase_Quantity_Limit' => 'Maximum Purchase Quantity Limit',
  'If_this_limit_is_exceeded,_customers_can_not_buy_the_item_in_a_single_purchase.' => 'If this limit is exceeded  customers can not buy the item in a single purchase.',
  'is_organic' => 'Is organic',
  'available_time_starts' => 'Available time starts',
  'available_time_ends' => 'Available time ends',
  'Admin_shares_the_same_percentage/amount_on_discount_as_he_takes_commissions_from_stores' => 'Admin shares the same percentage/amount on discount as he takes commissions from stores',
  'No variation added' => 'No variation added',
  'tags' => 'Tags',
  'search_tags' => 'Search tags',
  'Multiple Selection' => 'Multiple Selection',
  'Single Selection' => 'Single Selection',
  'select_a_module' => 'Select a module',
  'choice_title' => 'Choice title',
  'product_added_successfully' => 'Product added successfully',
  'Item List' => 'Item List',
  'all_stores' => 'All stores',
  'ex_:_search_item_name' => 'Ex : search item name',
  'limited_stock' => 'Limited stock',
  'edit_item' => 'Edit item',
  'delete_item' => 'Delete item',
  'Item Preview' => 'Item Preview',
  'edit_info' => 'Edit info',
  'of' => 'Of',
  'excellent_' => 'Excellent',
  'variations' => 'Variations',
  'product_reviews' => 'Product reviews',
  'Edit item' => 'Edit item',
  'item_update' => 'Item update',
  'Admin_shares_the_same_percentage/amount_on_discount_as_he_takes_commissions_from_stores.' => 'Admin shares the same percentage/amount on discount as he takes commissions from stores.',
  'Variant' => 'Variant',
  'Variant Price' => 'Variant Price',
  'stock' => 'Stock',
  'product_updated_successfully' => 'Product updated successfully',
  'item_reviews' => 'Item reviews',
  'Item Bulk Import' => 'Item Bulk Import',
  'items_bulk_import' => 'Items bulk import',
  '4. You can get store id, module id and unit id from their list, please input the right ids.' => '4. You can get store id  module id and unit id from their list  please input the right ids.',
  '5. For ecommerce item avaliable time start and end will be 00:00:00 and 23:59:59' => '5. For ecommerce item avaliable time start and end will be 00:00:00 and 23:59:59',
  '6. You can upload your product images in product folder from gallery, and copy image`s path.' => '6. You can upload your product images in product folder from gallery  and copy image`s path.',
  'download_spreadsheet_template' => 'Download spreadsheet template',
  'template_with_existing_data' => 'Template with existing data',
  'template_without_data' => 'Template without data',
  'import_items_file' => 'Import items file',
  'food_variations_generator' => 'Food variations generator',
  'generate' => 'Generate',
  'add_new_addon' => 'Add new addon',
  'new_addon' => 'New addon',
  'ex_:_addons_name' => 'Ex : addons name',
  'edit_addon' => 'Edit addon',
  'Want to delete this addon ?' => 'Want to delete this addon ?',
  'delete_addon' => 'Delete addon',
  'AddOn Bulk Import' => 'AddOn Bulk Import',
  'addons_bulk_import' => 'Addons bulk import',
  'you_want_to_hide_this_review_for_customer' => 'You want to hide this review for customer',
  'zone_deleted' => 'Zone deleted',
  'Withdraw Request' => 'Withdraw Request',
  'store_withdraw_transaction' => 'Store withdraw transaction',
  'ex_:_search_store_name' => 'Ex : search store name',
  'approved' => 'Approved',
  'denied' => 'Denied',
  'request_time' => 'Request time',
  'business_section' => 'Business section',
  'business_management' => 'Business management',
  'store_withdraws' => 'Store withdraws',
  'Withdraw Requests' => 'Withdraw Requests',
  'deliverymen_earning_provide' => 'Deliverymen earning provide',
  'Delivery Man Payments' => 'Delivery Man Payments',
  'report_and_analytics' => 'Report and analytics',
  'transection_report' => 'Transection report',
  'item_report' => 'Item report',
  'store_wise_report' => 'Store wise report',
  'expense_report' => 'Expense report',
  'limited_stock_item' => 'Limited stock item',
  'order_report' => 'Order report',
  'Withdraw information View' => 'Withdraw information View',
  'store_withdraw_information' => 'Store withdraw information',
  'note' => 'Note',
  'my_bank_info' => 'My bank info',
  'bank_name' => 'Bank name',
  'branch' => 'Branch',
  'holder_name' => 'Holder name',
  'account_no' => 'Account no',
  'store_info' => 'Store info',
  'owner_info' => 'Owner info',
  'Withdraw request process' => 'Withdraw request process',
  'request' => 'Request',
  'Note_about_transaction_or_request' => 'Note about transaction or request',
  'Submit' => 'Submit',
  'account_transaction' => 'Account transaction',
  'collect_cash_transaction' => 'Collect cash transaction',
  'collect_from' => 'Collect from',
  'select_deliveryman' => 'Select deliveryman',
  'Ex_:_Card' => 'Ex : Card',
  'Ex_:_1000' => 'Ex : 1000',
  'transaction_history' => 'Transaction history',
  'Search By Referance  or Name' => 'Search By Referance  or Name',
  'received_at' => 'Received at',
  'transaction_saved' => 'Transaction saved',
  'Accoutn transaction information' => 'Accoutn transaction information',
  'account_transaction_information' => 'Account transaction information',
  'deliveryman_info' => 'Deliveryman info',
  'transaction_information' => 'Transaction information',
  'method' => 'Method',
  'provide_deliverymen_earning' => 'Provide deliverymen earning',
  'ex_100' => 'Ex 100',
  'ex_cash' => 'Ex cash',
  'ex_collect_cash' => 'Ex collect cash',
  'deliverymen_earning_provide_table' => 'Deliverymen earning provide table',
  'earning_balance' => 'Earning balance',
  'No_vehicle_data_found' => 'No vehicle data found',
  'transaction_report' => 'Transaction report',
  'Search Data' => 'Search Data',
  'select_modules' => 'Select modules',
  'all_modules' => 'All modules',
  'All Time' => 'All Time',
  'This Year' => 'This Year',
  'Previous Year' => 'Previous Year',
  'This Month' => 'This Month',
  'This Week' => 'This Week',
  'Custom' => 'Custom',
  'Filter' => 'Filter',
  'Completed Transaction' => 'Completed Transaction',
  'When the order is successfully delivered full order amount goes to this section.' => 'When the order is successfully delivered full order amount goes to this section.',
  'Refunded Transaction' => 'Refunded Transaction',
  'If the order is successfully refunded, the full order amount goes to this section without the delivery fee and delivery tips.' => 'If the order is successfully refunded  the full order amount goes to this section without the delivery fee and delivery tips.',
  'Admin Earning' => 'Admin Earning',
  'Deducting the admin discount from the admin net income amount goes to this section.' => 'Deducting the admin discount from the admin net income amount goes to this section.',
  'Store Earning' => 'Store Earning',
  'If self-delivery is off, deducting delivery man earnings & admin commission order amount goes to store earnings otherwise deducting admin commission all order amount goes to this section.' => 'If self-delivery is off  deducting delivery man earnings & admin commission order amount goes to store earnings otherwise deducting admin commission all order amount goes to this section.',
  'Deliveryman Earning' => 'Deliveryman Earning',
  'Search by Order ID' => 'Search by Order ID',
  'customer_name' => 'Customer name',
  'total_item_amount' => 'Total item amount',
  'item_discount' => 'Item discount',
  'discounted_amount' => 'Discounted amount',
  'order_amount' => 'Order amount',
  'admin_discount' => 'Admin discount',
  'store_discount' => 'Store discount',
  'admin_commission' => 'Admin commission',
  'commision_on_delivery_charge' => 'Commision on delivery charge',
  'admin_net_income' => 'Admin net income',
  'store_net_income' => 'Store net income',
  'amount_received_by' => 'Amount received by',
  'cash on delivery' => 'Cash on delivery',
  'completed' => 'Completed',
  'parcel_order' => 'Parcel order',
  'freelance' => 'Freelance',
  'invalid_customer_data' => 'Invalid customer data',
  'ssl commerz payment' => 'Ssl commerz payment',
  'All Categories' => 'All Categories',
  'Item report table' => 'Item report table',
  'order_count' => 'Order count',
  'total_amount_sold' => 'Total amount sold',
  'total_discount_given' => 'Total discount given',
  'average_sale_value' => 'Average sale value',
  'average_ratings' => 'Average ratings',
  'Store Report' => 'Store Report',
  'Monitor_store’s_business_analytics_&_Reports' => 'Monitor store’s business analytics & Reports',
  'Summary Report' => 'Summary Report',
  'Sales Report' => 'Sales Report',
  'Order Report' => 'Order Report',
  'Registered Stores' => 'Registered Stores',
  'Incomplete' => 'Incomplete',
  'Completed' => 'Completed',
  'New Items' => 'New Items',
  'Average Order Value :' => 'Average Order Value :',
  'Average Value of completed orders.' => 'Average Value of completed orders.',
  'Completed payment statistics' => 'Completed payment statistics',
  'Cash Payments' => 'Cash Payments',
  'Digital Payments' => 'Digital Payments',
  'Total Stores' => 'Total Stores',
  'Total Order' => 'Total Order',
  'Total Delivered Order' => 'Total Delivered Order',
  'Total Amount' => 'Total Amount',
  'Completion Rate' => 'Completion Rate',
  'Ongoing Rate' => 'Ongoing Rate',
  'Cancelation Rate' => 'Cancelation Rate',
  'Refund Request' => 'Refund Request',
  'Action' => 'Action',
  'This report will show all the orders in which the admin discount has been used. The admin discount are: Free delivery over, store discount, Coupon discount & item discounts(partial according to order commission).' => 'This report will show all the orders in which the admin discount has been used. The admin discount are: Free delivery over  store discount  Coupon discount & item discounts(partial according to order commission).',
  'all_customers' => 'All customers',
  'expense_lists' => 'Expense lists',
  'Date & Time' => 'Date & Time',
  'Expense Type' => 'Expense Type',
  'Customer Name' => 'Customer Name',
  'expense amount' => 'Expense amount',
  'add_fund_bonus' => 'Add fund bonus',
  'discount_on_product' => 'Discount on product',
  'Invalid date range!' => 'Invalid date range!',
  'campaign_order' => 'Campaign order',
  'low_stock_report' => 'Low stock report',
  'All Zones' => 'All Zones',
  'Current stock' => 'Current stock',
  'edit_quantity' => 'Edit quantity',
  'in_progress_orders' => 'In progress orders',
  'Including accepted and processing orders' => 'Including accepted and processing orders',
  'on_the_way' => 'On the way',
  'failed_orders' => 'Failed orders',
  'not_received_yet' => 'Not received yet',
  'partial payment' => 'Partial payment',
  '' => '',
  'ssl commerz' => 'Ssl commerz',
  'digital payment' => 'Digital payment',
  'flutterwave' => 'Flutterwave',
  'business_Modules' => 'Business Modules',
  'business_Module_list' => 'Business Module list',
  'How it Works' => 'How it Works',
  'ex_:_Search_Module_by_Name' => 'Ex : Search Module by Name',
  'Add New Module' => 'Add New Module',
  'module_id' => 'Module id',
  'business_Module_type' => 'Business Module type',
  'Parcel' => 'Parcel',
  'Want_to_activate_this' => 'Want to activate this',
  'Business_Module?' => 'Business Module',
  'Want_to_deactivate_this' => 'Want to deactivate this',
  'If_you_activate_this_business_module,_all_its_features_and_functionalities_will_be_available_and_accessible_to_all_users.' => 'If you activate this business module  all its features and functionalities will be available and accessible to all users.',
  'If_you_deactivate_this_business_module,_all_its_features_and_functionalities_will_be_disabled_and_hidden_from_users.' => 'If you deactivate this business module  all its features and functionalities will be disabled and hidden from users.',
  'edit_Business_Module' => 'Edit Business Module',
  'food' => 'Food',
  'ecommerce' => 'Ecommerce',
  'Pharmacy' => 'Pharmacy',
  'pharmacy' => 'Pharmacy',
  'grocery' => 'Grocery',
  'Please go to settings and select module for this zone' => 'Please go to settings and select module for this zone',
  'Otherwise this zone won\'t function properly & will work show anything against this zone' => 'Otherwise this zone won t function properly & will work show anything against this zone',
  'How does it works ?' => 'How does it works ?',
  1 => '1',
  'Create_Business_Module' => 'Create Business Module',
  'To_create_a_new_business_module,_go_to:_‘Module_Setup’_→_‘Add_Business_Module.’' => 'To create a new business module  go to: ‘Module Setup’ → ‘Add Business Module.’',
  2 => '2',
  'Add_Module_to_Zone' => 'Add Module to Zone',
  'Go_to_‘Zone_Setup’→_‘Business_Zone_List’→_‘Zone_Settings’→_Choose_Payment_Method→Add_Business_Module_into_Zone_with_Parameters.' => 'Go to ‘Zone Setup’→ ‘Business Zone List’→ ‘Zone Settings’→ Choose Payment Method→Add Business Module into Zone with Parameters.',
  3 => '3',
  'Create_Stores' => 'Create Stores',
  'Select_your_Module_from_the_Module_Section,_Click_→_’Store_Management’→’Add_Store’→Add_Store_details_&_select_Zone_to_integrate_Module+Zone+Store.' => 'Select your Module from the Module Section  Click → ’Store Management’→’Add Store’→Add Store details & select Zone to integrate Module+Zone+Store.',
  'next' => 'Next',
  'business_settings' => 'Business settings',
  'zone_setup' => 'Zone setup',
  'system_module_setup' => 'System module setup',
  'module_setup' => 'Module setup',
  'add_Business_Module' => 'Add Business Module',
  'pages_setup' => 'Pages setup',
  'pages_&_social_media' => 'Pages & social media',
  'Social Media' => 'Social Media',
  'admin_landing_page_settings' => 'Admin landing page settings',
  'admin_landing_page' => 'Admin landing page',
  'react_landing_page' => 'React landing page',
  'flutter_landing_page' => 'Flutter landing page',
  'business_pages' => 'Business pages',
  'terms_and_condition' => 'Terms and condition',
  'privacy_policy' => 'Privacy policy',
  'about_us' => 'About us',
  'Refund Policy' => 'Refund Policy',
  'Cancelation Policy' => 'Cancelation Policy',
  'shipping_policy' => 'Shipping policy',
  'Shipping Policy' => 'Shipping Policy',
  'gallery' => 'Gallery',
  'system_management' => 'System management',
  '3rd_party_&_configurations' => '3rd party & configurations',
  '3rd_party' => '3rd party',
  'firebase_notification' => 'Firebase notification',
  'login_url_page' => 'Login url page',
  'react_site' => 'React site',
  'email_template' => 'Email template',
  'app_settings' => 'App settings',
  'clean_database' => 'Clean database',
  'system_addons' => 'System addons',
  'addon_menus' => 'Addon menus',
  'payment_setup' => 'Payment setup',
  'sms_setup' => 'Sms setup',
  'Do you want to logout?' => 'Do you want to logout',
  'cancel' => 'Cancel',
  'ok' => 'Ok',
  'Add new zone' => 'Add new zone',
  'Add_New_Business_Zone' => 'Add New Business Zone',
  'Create_&_connect_dots_in_a_specific_area_on_the_map_to_add_a_new_business_zone.' => 'Create & connect dots in a specific area on the map to add a new business zone.',
  'Use_this_‘Hand_Tool’_to_find_your_target_zone.' => 'Use this ‘Hand Tool’ to find your target zone.',
  'Use_this_‘Shape_Tool’_to_point_out_the_areas_and_connect_the_dots._Minimum_3_points/dots_are_required.' => 'Use this ‘Shape Tool’ to point out the areas and connect the dots. Minimum 3 points/dots are required.',
  'Choose_your_preferred_language_&_set_your_zone_name.' => 'Choose your preferred language & set your zone name.',
  'veg_non_veg' => 'Veg non veg',
  'business_Zone_name' => 'Business Zone name',
  'Write_a_New_Business_Zone_Name' => 'Write a New Business Zone Name',
  'Coordinates' => 'Coordinates',
  'draw_your_zone_on_the_map' => 'Draw your zone on the map',
  'zone_list' => 'Zone list',
  'Search_Business_Zone' => 'Search Business Zone',
  'zone_Id' => 'Zone Id',
  'digital_payment' => 'Digital payment',
  'cash_on_delivery' => 'Cash on delivery',
  'Want_to_activate_this_Zone?' => 'Want to activate this Zone',
  'Want_to_deactivate_this_Zone?' => 'Want to deactivate this Zone',
  'If_you_activate_this_zone,_Customers_can_see_all_stores_&_products_available_under_this_Zone_from_the_Customer_App_&_Website.' => 'If you activate this zone  Customers can see all stores & products available under this Zone from the Customer App & Website.',
  'If_you_deactivate_this_zone,_Customers_Will_NOT_see_all_stores_&_products_available_under_this_Zone_from_the_Customer_App_&_Website.' => 'If you deactivate this zone  Customers Will NOT see all stores & products available under this Zone from the Customer App & Website.',
  'Want_to_disable_‘Digital_Payment’?' => 'Want to disable ‘Digital Payment’',
  'If_yes,_the_digital_payment_option_will_be_hidden_during_checkout.' => 'If yes  the digital payment option will be hidden during checkout.',
  'Want_to_disable_‘Cash_On_Delivery’?' => 'Want to disable ‘Cash On Delivery’',
  'If_yes,_the_Cash_on_Delivery_option_will_be_hidden_during_checkout.' => 'If yes  the Cash on Delivery option will be hidden during checkout.',
  'edit_zone' => 'Edit zone',
  'Important!' => 'Important!',
  'The_Business_Zone_will_NOT_work_if_you_don’t_select_your_business_module_&_payment_method.' => 'The Business Zone will NOT work if you don’t select your business module & payment method.',
  'Want_to_Delete_this_Zone?' => 'Want to Delete this Zone',
  'If_yes,_all_its_modules,_stores,_and_products_will_be_DELETED_FOREVER.' => 'If yes  all its modules  stores  and products will be DELETED FOREVER.',
  'delete_zone' => 'Delete zone',
  'Want_to_enable_‘Digital_Payment’?' => 'Want to enable ‘Digital Payment’',
  'If_yes,_Customers_can_choose_the_‘Digital_Payment’_option_during_checkout.' => 'If yes  Customers can choose the ‘Digital Payment’ option during checkout.',
  'Want_to_enable_‘Cash_On_Delivery’?' => 'Want to enable ‘Cash On Delivery’',
  'If_yes,_Customers_can_choose_the_‘Cash_On_Delivery’_option_during_checkout.' => 'If yes  Customers can choose the ‘Cash On Delivery’ option during checkout.',
  'New_Business_Zone_Created_Successfully!' => 'New Business Zone Created Successfully!',
  'NEXT_IMPORTANT_STEP:_You_need_to_select_‘Payment_Method’_and_add_‘Business_Modules’_with_other_details_from_the_Zone_Settings._If_you_don’t_finish_the_setup,_the_Zone_you_created_won’t_function_properly.' => 'NEXT IMPORTANT STEP: You need to select ‘Payment Method’ and add ‘Business Modules’ with other details from the Zone Settings. If you don’t finish the setup  the Zone you created won’t function properly.',
  'Don\'t show this anymore' => 'Don t show this anymore',
  'I will do it later' => 'I will do it later',
  'Go_to_zone_Settings' => 'Go to zone Settings',
  'By switching the status to “ON”,  this zone and under all the functionality of this zone will be turned on' => 'By switching the status to “ON”   this zone and under all the functionality of this zone will be turned on',
  'In the user app & website all stores & products  already assigned under this zone will show to the customers' => 'In the user app & website all stores & products  already assigned under this zone will show to the customers',
  'zone_added_successfully' => 'Zone added successfully',
  'Zone Wise Module Setup' => 'Zone Wise Module Setup',
  'Zone_Settings' => 'Zone Settings',
  'Select_Payment_Method' => 'Select Payment Method',
  'NB:_MUST_select_at_least_‘one’_payment_method.' => 'NB: MUST select at least ‘one’ payment method.',
  'Choose_Business_Module' => 'Choose Business Module',
  'Module Name' => 'Module Name',
  'per_km_delivery_charge' => 'Per km delivery charge',
  'Minimum delivery charge' => 'Minimum delivery charge',
  'Maximum delivery charge' => 'Maximum delivery charge',
  'maximum_cod_order_amount' => 'Maximum cod order amount',
  'enter_Amount' => 'Enter Amount',
  'maximum delivery charge' => 'Maximum delivery charge',
  'set_maximum_cod_order_amount' => 'Set maximum cod order amount',
  'Set charge from parcel category' => 'Set charge from parcel category',
  'business_modules' => 'Business modules',
  'Add_New_Business_Module' => 'Add New Business Module',
  '*Set_up_your_New_Business_Module_type_theme_icon_&_thumbnail.' => '*Set up your New Business Module type theme icon & thumbnail.',
  'Note' => 'Note',
  'Don’t_forget_to_click_the_‘Add_Module’_button_below_to_save_the_new_business_module.' => 'Don’t forget to click the ‘Add Module’ button below to save the new business module.',
  'Business_Module_name' => 'Business Module name',
  'Ex:_Grocery,eCommerce,Pharmacy,etc.' => 'Ex: Grocery eCommerce Pharmacy etc.',
  'Business_Module_description' => 'Business Module description',
  'Write_a_short_description_of_your_new_business_module_within_100_words_(550_characters)' => 'Write a short description of your new business module within 100 words (550 characters)',
  'business_module_type' => 'Business module type',
  'select_business_module_type' => 'Select business module type',
  'business_module_type_change_warning' => 'Business module type change warning',
  'Choose_a_Theme_for_the_module' => 'Choose a Theme for the module',
  'icon' => 'Icon',
  'thumbnail' => 'Thumbnail',
  'Add_Module' => 'Add Module',
  'Update_Business_Module' => 'Update Business Module',
  'Edit_Business_Module' => 'Edit Business Module',
  'choose_theme' => 'Choose theme',
  'Save_changes' => 'Save changes',
  'business_information' => 'Business information',
  'order_settings' => 'Order settings',
  'refund_settings' => 'Refund settings',
  'Languages' => 'Languages',
  'landing_page' => 'Landing page',
  'don’t_forget_to_click_the_‘Save Information’_button_below_to_save_changes.' => 'Don’t forget to click the ‘Save Information’ button below to save changes.',
  'maintenance_mode' => 'Maintenance mode',
  'maintenance_txt' => 'maintenance txt',
  'Company Information' => 'Company Information',
  'company_name' => 'Company name',
  'new_company' => 'New company',
  'Ex: +3264124565' => 'Ex: +3264124565',
  'country' => 'Country',
  'Ex: address' => 'Ex: address',
  'click_on_the_map_select_your_defaul_location' => 'Click on the map select your defaul location',
  '3:1' => '3:1',
  'Favicon' => 'Favicon',
  'General Settings' => 'General Settings',
  'time_zone' => 'Time zone',
  'time_format' => 'Time format',
  '12_hour' => '12 hour',
  '24_hour' => '24 hour',
  'Currency Symbol' => 'Currency Symbol',
  'Currency Position' => 'Currency Position',
  'Left' => 'Left',
  'Right' => 'Right',
  'Digit after decimal point' => 'Digit after decimal point',
  'how_many_fractional_digit_to_show_after_decimal_value' => 'How many fractional digit to show after decimal value',
  'ex_:_2' => 'Ex : 2',
  'Copyright Text' => 'Copyright Text',
  'make_visitors_aware_of_your_business‘s_rights_&_legal_information.' => 'Make visitors aware of your business‘s rights & legal information.',
  'Ex_:_Copyright_Text' => 'Ex : Copyright Text',
  'Cookies Text' => 'Cookies Text',
  'Ex_:_Cookies_Text' => 'Ex : Cookies Text',
  'Business_Rules_setup' => 'Business Rules setup',
  'Default_Commission_Rate_On_Order' => 'Default Commission Rate On Order',
  'Set_up_‘Default_Commission_Rate’_on_every_Order._Admin_can_also_set_store-wise_different_commission_rates_from_respective_store_settings.' => 'Set up ‘Default Commission Rate’ on every Order. Admin can also set store-wise different commission rates from respective store settings.',
  'Commission_Rate_On_Delivery_Charge' => 'Commission Rate On Delivery Charge',
  'Set_a_default_‘Commission_Rate’_for_freelance_deliverymen_(under_admin)_on_every_deliveryman. ' => 'Set a default ‘Commission Rate’ for freelance deliverymen (under admin) on every deliveryman.',
  'Who_Will_Confirm_Order?' => 'Who Will Confirm Order',
  'After_a_customer_order_placement,_Admin_can_define_who_will_confirm_the_order_first-_Deliveryman_or_Store?_For_example,_if_you_choose_‘Delivery_man’,_the_deliveryman_nearby_will_confirm_the_order_and_forward_it_to_the_related_store_to_process_the_order._It_works_vice-versa_if_you_choose_‘Store’.' => 'After a customer order placement  Admin can define who will confirm the order first- Deliveryman or Store  For example  if you choose ‘Delivery man’  the deliveryman nearby will confirm the order and forward it to the related store to process the order. It works vice-versa if you choose ‘Store’.',
  'Include_TAX_Amount' => 'Include TAX Amount',
  'If_enabled,_the_customer_will_see_the_total_product_price,_including_VAT/Tax._If_it’s_disabled,_the_VAT/Tax_will_be_added_separately_with_the_total_cost_of_the_product.' => 'If enabled  the customer will see the total product price  including VAT/Tax. If it’s disabled  the VAT/Tax will be added separately with the total cost of the product.',
  'Want_to' => 'Want to',
  '‘Include_Tax_Amount?’' => '‘Include Tax Amount ?’',
  'Want_to_disable' => 'Want to disable',
  'Tax_Amount’?' => 'Tax Amount’',
  'If_you_enable_it,_customers_will_see_the_product_Price_including_Tax,_during_checkout. ' => 'If you enable it  customers will see the product Price including Tax  during checkout.',
  'If_you_disable_it,_customers_will_see_the_product_or_service_price_without_Tax,_during_checkout.' => 'If you disable it  customers will see the product or service price without Tax  during checkout.',
  'minimum_shipping_charge' => 'Minimum shipping charge',
  'per_km_shipping_charge' => 'Per km shipping charge',
  'Customer’s_Food_Preference' => 'Customer’s Food Preference',
  'If_this_feature_is_active,_customers_can_filter_food_according_to_their_preference_from_the_Customer_App_or_Website.' => 'If this feature is active  customers can filter food according to their preference from the Customer App or Website.',
  'Want_to_enable_the' => 'Want to enable the',
  '‘Veg/Non-Veg’_feature?' => '‘Veg/Non-Veg’ feature',
  'the_Veg/Non-Veg_Feature?' => 'The Veg/Non-Veg Feature',
  'If_you_enable_this,_customers_can_filter_food_items_by_choosing_food_from_the_Veg/Non-Veg_feature.' => 'If you enable this  customers can filter food items by choosing food from the Veg/Non-Veg feature.',
  'If_you_disable_this,_the_Veg/Non-Veg_feature_will_be_hidden_in_the_Customer_App_&_Website.' => 'If you disable this  the Veg/Non-Veg feature will be hidden in the Customer App & Website.',
  'free_delivery_over' => 'Free delivery over',
  'Set_a_minimum_order_value_for_automated_free_delivery._If_the_minimum_amount_is_exceeded,_the_Delivery_Fee_is_deducted_from_Admin’s_commission_and_added_to_Admin’s_expense.' => 'Set a minimum order value for automated free delivery. If the minimum amount is exceeded  the Delivery Fee is deducted from Admin’s commission and added to Admin’s expense.',
  'free_over_delivery_message' => 'Free over delivery message',
  'Want_to_enable_Free_Delivery_on_Minimum_Orders?' => 'Want to enable Free Delivery on Minimum Orders',
  'Want_to_disable_Free_Delivery_on_Minimum_Order?' => 'Want to disable Free Delivery on Minimum Order',
  'If_you_enable_this,_customers_can_get_FREE_Delivery_by_fulfilling_the_minimum_order_requirement.' => 'If you enable this  customers can get FREE Delivery by fulfilling the minimum order requirement.',
  'If_you_disable_this,_the_FREE_Delivery_option_will_be_hidden_from_the_Customer_App_or_Website.' => 'If you disable this  the FREE Delivery option will be hidden from the Customer App or Website.',
  'Order_Notification_for_Admin' => 'Order Notification for Admin',
  'Admin_will_get_a_pop-up_notification_with_sounds_for_any_order_placed_by_customers.' => 'Admin will get a pop-up notification with sounds for any order placed by customers.',
  'customer_varification_toggle' => 'Customer varification toggle',
  'Want_to_enable' => 'Want to enable',
  'Order_Notification_for_Admin?' => 'Order Notification for Admin',
  'If_you_enable_this,_the_Admin_will_receive_a_Notification_for_every_order_placed.' => 'If you enable this  the Admin will receive a Notification for every order placed.',
  'If_you_disable_this,_the_Admin_will_NOT_receive_a_Notification_for_every_order_placed.' => 'If you disable this  the Admin will NOT receive a Notification for every order placed.',
  'If_enabled,_customers_need_to_pay_an_extra_charge_while_checking_out_orders.' => 'If enabled  customers need to pay an extra charge while checking out orders.',
  'Want_to_enable_additional_charge?' => 'Want to enable additional charge',
  'Want_to_disable_additional_charge?' => 'Want to disable additional charge',
  'If_you_enable_this,_additional_charge_will_be_added_with_order_amount,_it_will_be_added_in_admin_wallet' => 'If you enable this  additional charge will be added with order amount  it will be added in admin wallet',
  'If_you_disable_this,_additional_charge_will_not_be_added_with_order_amount.' => 'If you disable this  additional charge will not be added with order amount.',
  'additional_charge_name' => 'Additional charge name',
  'Set_a_name_for_the_additional_charge,_e.g._“Processing_Fee”.' => 'Set a name for the additional charge  e.g. “Processing Fee”.',
  'Ex:_Processing_Fee' => 'Ex: Processing Fee',
  'charge_amount' => 'Charge amount',
  'Set_the_value_(amount)_customers_need_to_pay_as_additional_charge.' => 'Set the value (amount) customers need to pay as additional charge.',
  'If_enabled,_customers_can_make_partial_payments._For_example,_a_customer_can_pay_$20_initially_out_of_their_$50_payment_&_use_other_payment_methods_for_the_rest._Partial_payments_must_be_made_through_their_wallets.' => 'If enabled  customers can make partial payments. For example  a customer can pay $20 initially out of their $50 payment & use other payment methods for the rest. Partial payments must be made through their wallets.',
  'partial_payment_?' => 'Partial payment ?',
  'If_you_enable_this,_customers_can_choose_partial_payment_during_checkout.' => 'If you enable this  customers can choose partial payment during checkout.',
  'If_you_disable_this,_the_partial_payment_feature_will_be_hidden.' => 'If you disable this  the partial payment feature will be hidden.',
  'Can_Pay_the_Rest_Amount_using' => 'Can Pay the Rest Amount using',
  'Set_the_method(s)_that_customers_can_pay_the_remainder_after_partial_payment.' => 'Set the method(s) that customers can pay the remainder after partial payment.',
  'cod' => 'COD',
  'both' => 'Both',
  'save_information' => 'Save information',
  'all_your_apps_and_customer_website_will_be_disabled_until_you_‘Turn_Off’ _maintenance_mode.' => 'All your apps and customer website will be disabled until you ‘Turn Off’  maintenance mode.',
  'order_delivery_verification' => 'Order delivery verification',
  'When_a_deliveryman_arrives_for_delivery,_Customers_will_get_a_4-digit_verification_code_on_the_order_details_section_in_the_Customer_App_and_needs_to_provide_the_code_to_the_delivery_man_to_verify_the_order.' => 'When a deliveryman arrives for delivery  Customers will get a 4-digit verification code on the order details section in the Customer App and needs to provide the code to the delivery man to verify the order.',
  'order_varification_toggle' => 'Order varification toggle',
  'Delivery_Verification?' => 'Delivery Verification',
  'If you enable this, the Deliveryman has to verify the order during delivery through a 4-digit verification code.' => 'If you enable this  the Deliveryman has to verify the order during delivery through a 4-digit verification code.',
  'If you disable this, Deliveryman will deliver the order and update the status. He doesn’t need to verify the order with any code.' => 'If you disable this  Deliveryman will deliver the order and update the status. He doesn’t need to verify the order with any code.',
  'Place_Order_by_Prescription' => 'Place Order by Prescription',
  'With_this_feature,_customers_can_place_an_order_by_uploading_prescription._Stores_can_enable/disable_this_feature_from_the_store_settings_if_needed.' => 'With this feature  customers can place an order by uploading prescription. Stores can enable/disable this feature from the store settings if needed.',
  'prescription_order_status' => 'Prescription order status',
  'Place_Order_by_Prescription?' => 'Place Order by Prescription',
  'If_you_enable_this,_customers_can_place_an_order_by_simply_uploading_their_prescriptions_in_the_Pharmacy_module_from_the_Customer_App_or_Website._Stores_can_enable/disable_this_feature_from_store_settings_if_needed.' => 'If you enable this  customers can place an order by simply uploading their prescriptions in the Pharmacy module from the Customer App or Website. Stores can enable/disable this feature from store settings if needed.',
  'If_disabled,_this_feature_will_be_hidden_from_the_Customer_App,_Website,_and_Store_App_&_Panel.' => 'If disabled  this feature will be hidden from the Customer App  Website  and Store App & Panel.',
  'If_you_enable_this_feature,_customers_can_choose_‘Home_Delivery’_and_get_the_product_delivered_to_their_preferred_location.' => 'If you enable this feature  customers can choose ‘Home Delivery’ and get the product delivered to their preferred location.',
  'Home_Delivery?' => 'Home Delivery',
  'If_you_enable_this,_customers_can_use_Home_Delivery_Option_during_checkout_from_the_Customer_App_or_Website.' => 'If you enable this  customers can use Home Delivery Option during checkout from the Customer App or Website.',
  'If_you_disable_this,_the_Home_Delivery_feature_will_be_hidden_from_the_customer_app_and_website.' => 'If you disable this  the Home Delivery feature will be hidden from the customer app and website.',
  'Takeaway' => 'Takeaway',
  'If_you_enable_this_feature,_customers_can_place_an_order_and_request_‘Takeaways’_or_‘self-pick-up’_from_stores.' => 'If you enable this feature  customers can place an order and request ‘Takeaways’ or ‘self-pick-up’ from stores.',
  'the_Takeaway_feature?' => 'The Takeaway feature',
  'If_you_enable_this,_customers_can_use_the_Takeaway_feature_during_checkout_from_the_Customer_App_or_Website.' => 'If you enable this  customers can use the Takeaway feature during checkout from the Customer App or Website.',
  'If_you_disable_this,_the_Takeaway_feature_will_be_hidden_from_the_Customer_App_or_Website.' => 'If you disable this  the Takeaway feature will be hidden from the Customer App or Website.',
  'Scheduled_Delivery' => 'Scheduled Delivery',
  'With_this_feature,_customers_can_choose_their_preferred_delivery_slot._Customers_can_select_a_delivery_slot_for_ASAP_or_a_specific_date_(within_2_days_from_the_order).' => 'With this feature  customers can choose their preferred delivery slot. Customers can select a delivery slot for ASAP or a specific date (within 2 days from the order).',
  'Scheduled Delivery?' => 'Scheduled Delivery',
  'If_you_enable_this,_customers_can_choose_a_suitable_delivery_schedule_during checkout.' => 'If you enable this  customers can choose a suitable delivery schedule during checkout.',
  'If_you_disable_this,_the_Scheduled_Delivery_feature_will_be_hidden.' => 'If you disable this  the Scheduled Delivery feature will be hidden.',
  'Time_Interval_for_Scheduled_Delivery' => 'Time Interval for Scheduled Delivery',
  'By_activating_this_feature,_customers_can_choose_their_suitable_delivery_slot_according_to_a_30-minute_or_1-hour_interval_set_by_the_Admin.' => 'By activating this feature  customers can choose their suitable delivery slot according to a 30-minute or 1-hour interval set by the Admin.',
  'Hour' => 'Hour',
  'Order Cancelation Messages' => 'Order Cancelation Messages',
  'Order Cancellation Reason' => 'Order Cancellation Reason',
  'Ex:_Item_is_Broken' => 'Ex: Item is Broken',
  'User Type' => 'User Type',
  'When this field is active, user can cancel an order with proper reason.' => 'When this field is active  user can cancel an order with proper reason.',
  'select_user_type' => 'Select user type',
  '*Users_cannot_cancel_an_order_if_the_Admin_does_not_specify_a_cause_for_cancellation,_even_though_they_see_the_‘Cancel_Order‘_option._So_Admin_MUST_provide_a_proper_Order_Cancellation_Reason_and_select_the_related_user.' => '*Users cannot cancel an order if the Admin does not specify a cause for cancellation  even though they see the ‘Cancel Order‘ option. So Admin MUST provide a proper Order Cancellation Reason and select the related user.',
  'order_cancellation_reason_list' => 'Order cancellation reason list',
  'Reason' => 'Reason',
  'If_you_want_to_delete_this_reason,_please_confirm_your_decision.' => 'If you want to delete this reason  please confirm your decision.',
  'order_cancellation_reason' => 'Order cancellation reason',
  'Update' => 'Update',
  'store_setup' => 'Store setup',
  '*If_the_Admin_enables_the_‘Refund_Request_Mode’,_customers_can_request_a_refund.' => '*If the Admin enables the ‘Refund Request Mode’  customers can request a refund.',
  'Refund Request_Mode' => 'Refund Request Mode',
  '*Customers_cannot_request_a_Refund_if_the_Admin_does_not_specify_a_cause_for_cancellation,_even_though_they_see_the_Cancel_Order_option._So_Admin_MUST_provide_a_proper_Refund_Reason_and_select_the_related_user.' => '*Customers cannot request a Refund if the Admin does not specify a cause for cancellation  even though they see the Cancel Order option. So Admin MUST provide a proper Refund Reason and select the related user.',
  'Add a Refund Reason' => 'Add a Refund Reason',
  'Add Now' => 'Add Now',
  'Refund Reason List' => 'Refund Reason List',
  'Want to delete this refund reason ?' => 'Want to delete this refund reason ?',
  'Refund_Reason_Update' => 'Refund Reason Update',
  'Can_a_Store_Cancel_Order?' => 'Can a Store Cancel Order',
  'Admin_can_enable/disable_Store’s_order_cancellation_option.' => 'Admin can enable/disable Store’s order cancellation option.',
  'store_self_registration' => 'Store self registration',
  'A_store_can_send_a_registration_request_through_their_store_or_customer.' => 'A store can send a registration request through their store or customer.',
  'Store_Self_Registration?' => 'Store Self Registration',
  'If_you_enable_this,_Stores_can_do_self-registration_from_the_store_or_customer_app_or_website.' => 'If you enable this  Stores can do self-registration from the store or customer app or website.',
  'If_you_disable_this,_the_Store_Self-Registration_feature_will_be_hidden_from_the_store_or_customer_app,_website,_or_admin_landing_page.' => 'If you disable this  the Store Self-Registration feature will be hidden from the store or customer app  website  or admin landing page.',
  'delivery_man_settings' => 'Delivery man settings',
  'Tips_for_Deliveryman' => 'Tips for Deliveryman',
  'Customers_can_give_tips_to_deliverymen_during_checkout_from_the_Customer_App_&_Website._Admin_has_no_commission_on_it.' => 'Customers can give tips to deliverymen during checkout from the Customer App & Website. Admin has no commission on it.',
  'dm_tips_model_hint' => 'Dm tips model hint',
  'Tips_for_Deliveryman_feature?' => 'Tips for Deliveryman feature',
  'If_you_enable_this,_Customers_can_give_tips_to_a_deliveryman_during_checkout.' => 'If you enable this  Customers can give tips to a deliveryman during checkout.',
  'If_you_disable_this,_the_Tips_for_Deliveryman_feature_will_be_hidden_from_the_Customer_App_and_Website.' => 'If you disable this  the Tips for Deliveryman feature will be hidden from the Customer App and Website.',
  'Show Earnings in App' => 'Show Earnings in App',
  'With_this_feature,_Deliverymen_can_see_their_earnings_on_a_specific_order_while_accepting_it.' => 'With this feature  Deliverymen can see their earnings on a specific order while accepting it.',
  'Show_Earnings_in_App?' => 'Show Earnings in App',
  'If_you_enable_this,_Deliverymen_can_see_their_earning_per_order_request_from_the_Order_Details_page_in_the_Deliveryman_App.' => 'If you enable this  Deliverymen can see their earning per order request from the Order Details page in the Deliveryman App.',
  'If_you_disable_this,_the_feature_will_be_hidden_from_the_Deliveryman_App.' => 'If you disable this  the feature will be hidden from the Deliveryman App.',
  'dm_self_registration' => 'Dm self registration',
  'With_this_feature,_deliverymen_can_register_themselves_from_the_Customer_App,_Website_or_Deliveryman_App_or_Admin_Landing_Page._The_admin_will_receive_an_email_notification_and_can_accept_or_reject_the_request.' => 'With this feature  deliverymen can register themselves from the Customer App  Website or Deliveryman App or Admin Landing Page. The admin will receive an email notification and can accept or reject the request.',
  'Deliveryman_Self_Registration?' => 'Deliveryman Self Registration',
  'If_you_enable_this,_users_can_register_as_Deliverymen_from_the_Customer_App,_Website_or_Deliveryman_App_or_Admin_Landing_Page.' => 'If you enable this  users can register as Deliverymen from the Customer App  Website or Deliveryman App or Admin Landing Page.',
  'If_you_disable_this,_this_feature_will_be_hidden_from_the_Customer_App,_Website_or_Deliveryman_App_or_Admin_Landing_Page.' => 'If you disable this  this feature will be hidden from the Customer App  Website or Deliveryman App or Admin Landing Page.',
  'Maximum Assigned Order Limit' => 'Maximum Assigned Order Limit',
  'Set_the_maximum_order_limit_a_Deliveryman_can_take_at_a_time.' => 'Set the maximum order limit a Deliveryman can take at a time.',
  'dm_maximum_order_hint' => 'Dm maximum order hint',
  'Can_A_Deliveryman_Cancel_Order?' => 'Can A Deliveryman Cancel Order',
  'Admin_can_enable/disable_Deliveryman’s_order_cancellation_option_in_the_respective_app.' => 'Admin can enable/disable Deliveryman’s order cancellation option in the respective app.',
  'dm_cancel_order_hint' => 'Dm cancel order hint',
  'Take_Picture_For_Completing_Delivery' => 'Take Picture For Completing Delivery',
  'If_enabled,_deliverymen_will_see_an_option_to_take_pictures_of_the_delivered_products_when_he_swipes_the_delivery_confirmation_slide.' => 'If enabled  deliverymen will see an option to take pictures of the delivered products when he swipes the delivery confirmation slide.',
  'dm_picture_upload_status' => 'Dm picture upload status',
  'picture_upload_before_complete?' => 'Picture upload before complete',
  'If_you_enable_this,_delivery_man_can_upload_order_proof_before_order_delivery.' => 'If you enable this  delivery man can upload order proof before order delivery.',
  'If_you_disable_this,_this_feature_will_be_hidden_from_the_delivery_man_app.' => 'If you disable this  this feature will be hidden from the delivery man app.',
  'customer_settings' => 'Customer settings',
  'Here,_customers_can_store_their_refunded_order_amount,_referral_earnings,_and_loyalty_points.' => 'Here  customers can store their refunded order amount  referral earnings  and loyalty points.',
  'show_hide_food_menu' => 'Show hide food menu',
  'Customer Can Earn & Buy From Wallet' => 'Customer Can Earn & Buy From Wallet',
  'With_this_feature,_customers_can_have_virtual_wallets_in_their_account_via_Customer_App_&_Website._They_can_also_earn_(via_referral,_refund,_or_loyalty_points)_and_buy_with_the_wallet’s_amount.' => 'With this feature  customers can have virtual wallets in their account via Customer App & Website. They can also earn (via referral  refund  or loyalty points) and buy with the wallet’s amount.',
  'the_Wallet_feature?' => 'The Wallet feature',
  'If_you_enable_this,_Customers_can_see_&_use_the_Wallet_option_from_their_profile_in_the_Customer_App_&_Website.' => 'If you enable this  Customers can see & use the Wallet option from their profile in the Customer App & Website.',
  'If_you_disable_this,_the_Wallet_feature_will_be_hidden_from_the_Customer_App_&_Website.' => 'If you disable this  the Wallet feature will be hidden from the Customer App & Website.',
  'refund_to_wallet' => 'Refund to wallet',
  'If_it’s_enabled,_Customers_will_automatically_receive_the_refunded_amount_in_their_wallets._But_if_it’s_disabled,_the_Admin_will_handle_the_Refund_Request_in_his_convenient_transaction_channel.' => 'If it’s enabled  Customers will automatically receive the refunded amount in their wallets. But if it’s disabled  the Admin will handle the Refund Request in his convenient transaction channel.',
  'Refund_to_Wallet_feature?' => 'Refund to Wallet feature',
  'If_you_enable_this,_Customers_will_automatically_receive_the_refunded_amount_in_their_wallets.' => 'If you enable this  Customers will automatically receive the refunded amount in their wallets.',
  'If_you_disable_this,_the_Admin_will_handle_the_Refund_Request_in_his_convenient_transaction_channel.' => 'If you disable this  the Admin will handle the Refund Request in his convenient transaction channel.',
  'customer_can_add_fund_to_wallet' => 'Customer can add fund to wallet',
  'With_this_feature,_customers_can_add_fund_to_wallet_if_the_payment_module_is_available.' => 'With this feature  customers can add fund to wallet if the payment module is available.',
  'add_fund_status' => 'Add fund status',
  'add_fund_to_Wallet_feature?' => 'Add fund to Wallet feature',
  'If_you_enable_this,_Customers_can_add_fund_to_wallet_using_payment_module' => 'If you enable this  Customers can add fund to wallet using payment module',
  'If_you_disable_this,_add_fund_to_wallet_will_be_hidden_from_the_Customer_App_&_Website.' => 'If you disable this  add fund to wallet will be hidden from the Customer App & Website.',
  'Referral Earning' => 'Referral Earning',
  'Existing_Customers_can_share_a_referral_code_with_others_to_earn_a_referral_bonus._For_this,_the_new_user_MUST_sign_up_using_the_referral_code_and_make_their_first_purchase.' => 'Existing Customers can share a referral code with others to earn a referral bonus. For this  the new user MUST sign up using the referral code and make their first purchase.',
  'Customer Can Earn & Buy From Referral' => 'Customer Can Earn & Buy From Referral',
  'Referral_Earning?' => 'Referral Earning',
  'If_you_enable_this,_Customers_can_earn_points_by_referring_others_to_sign_up_&_purchase_from_your_business.' => 'If you enable this  Customers can earn points by referring others to sign up & purchase from your business.',
  'If_you_disable_this,_the_referral-earning_feature_will_be_hidden_from_the_Customer_App_&_Website.' => 'If you disable this  the referral-earning feature will be hidden from the Customer App & Website.',
  'Earning Per Referral ($)' => 'Earning Per Referral ($)',
  'Customer Verification' => 'Customer Verification',
  'If_you_activate_this_feature,_customers_need_to_verify_their_account_information_via_OTP_during_the_signup_process.' => 'If you activate this feature  customers need to verify their account information via OTP during the signup process.',
  'Customer_Verification?' => 'Customer Verification',
  'If_you_enable_this,_Customers_have_to_verify_their_account_via_OTP.' => 'If you enable this  Customers have to verify their account via OTP.',
  'If_you_disable_this,_Customers_don’t_need_to_verify_their_account_via_OTP.' => 'If you disable this  Customers don’t need to verify their account via OTP.',
  'Loyalty Point' => 'Loyalty Point',
  'Customer Can Earn Loyalty Point' => 'Customer Can Earn Loyalty Point',
  'Customer will see loyalty point option in his profile settings & can earn & convert this point to wallet money' => 'Customer will see loyalty point option in his profile settings & can earn & convert this point to wallet money',
  'Customer will no see loyalty point option from his profile settings' => 'Customer will no see loyalty point option from his profile settings',
  'Loyalty Point Earn Per Order (%)' => 'Loyalty Point Earn Per Order (%)',
  'item_purchase_point_hint' => 'Item purchase point hint',
  'Minimum Loyalty Point Required to Transfer' => 'Minimum Loyalty Point Required to Transfer',
  'point_to_currency_exchange_rate' => 'Point to currency exchange rate',
  'Save Information' => 'Save Information',
  'language' => 'Language',
  'language_list' => 'Language list',
  'add_new_language' => 'Add new language',
  'Id' => 'Id',
  'Code' => 'Code',
  'default_status' => 'Default status',
  'new_language' => 'New language',
  'direction' => 'Direction',
  'Add' => 'Add',
  'status_updated_successfully' => 'Status updated successfully',
  'Are you sure to delete this' => 'Are you sure to delete this',
  'You will not be able to revert this' => 'You will not be able to revert this',
  'Yes_delete it' => 'Yes delete it',
  'default language can not be deleted! to delete change the default language first!' => 'Default language can not be deleted! to delete change the default language first!',
  'default language can not be updated! to update change the default language first!' => 'Default language can not be updated! to update change the default language first!',
  'admin_default_landing_page' => 'Admin default landing page',
  'You_can_turn_off/on_system-provided_landing_page' => 'You can turn off/on system-provided landing page',
  'Want_to_Integrate_Your_Own_Customised_Landing_Page_?' => 'Want to Integrate Your Own Customised Landing Page ?',
  'Read_Instructions' => 'Read Instructions',
  'Integrate_Your_Landing_Page_Via' => 'Integrate Your Landing Page Via',
  'You_can_upload_your_landing_page_either_using_URL_or_File_Upload' => 'You can upload your landing page either using URL or File Upload',
  'url' => 'Url',
  'file_upload' => 'File upload',
  'none' => 'None',
  'landing_page_url' => 'Landing page url',
  'Ex: https://6ammart-web.6amtech.com/' => 'Ex: https://6ammart-web.6amtech.com/',
  'instructions' => 'Instructions',
  'Upload_content_as_a_single_ZIP_file_and_the_file_name_must_be' => 'Upload content as a single ZIP file and the file name must be',
  'Currently_you_are_using_6amMart_Default_Admin_Landing_Page_Theme.' => 'Currently you are using 6amMart Default Admin Landing Page Theme.',
  'Visit_Landing_Page' => 'Visit Landing Page',
  'Reset' => 'Reset',
  'Save_Information' => 'Save Information',
  'If_you_want_to_set_up_your_own_landing_page_please_follow_tha_instructions_below' => 'If you want to set up your own landing page please follow tha instructions below',
  'You_can_add_your_customised_landing_page_via_URL_or_upload_ZIP_file_of_the_landing_page.' => 'You can add your customised landing page via URL or upload ZIP file of the landing page.',
  'If_you_want_to_use_URL_option._Just_host_you_landing_page_and_copy_the_page_URL_and_click_save_information.' => 'If you want to use URL option. Just host you landing page and copy the page URL and click save information.',
  'If_you_want_to_Upload_your_landing_page_source_code_file.' => 'If you want to Upload your landing page source code file.',
  'a._Create_an_html_file_named' => 'A. Create an html file named',
  '_and_insert_your_landing_page_design_code_and_make_a_zip_file.' => 'and insert your landing page design code and make a zip file.',
  'b._upload_the_zip_file_in_file_upload_section_and_click_save_information.' => 'B. upload the zip file in file upload section and click save information.',
  'updated successfully!' => 'Updated successfully!',
  'Want_to_Turn_Off_the_Default_Admin_Landing_Page_?' => 'Want to Turn Off the Default Admin Landing Page ?',
  'If_disabled,_the_landing_page_won’t_be_visible_to_anyone' => 'If disabled  the landing page won’t be visible to anyone',
  'social_media' => 'Social media',
  'Instagram' => 'Instagram',
  'Facebook' => 'Facebook',
  'Twitter' => 'Twitter',
  'LinkedIn' => 'LinkedIn',
  'Pinterest' => 'Pinterest',
  'social_media_link' => 'Social media link',
  'link' => 'Link',
  'social_media_required' => 'Social media required',
  'social_media_exist' => 'Social media exist',
  'social_media_inserted' => 'Social media inserted',
  'social_media_updated' => 'Social media updated',
  'are_u_sure_want_to_delete' => 'Are u sure want to delete',
  'social_media_deleted' => 'Social media deleted',
  'status_updated' => 'Status updated',
  'admin_landing_pages' => 'Admin landing pages',
  'fixed_data' => 'Fixed data',
  'promotional_section' => 'Promotional section',
  'feature_list' => 'Feature list',
  'earn_money' => 'Earn money',
  'why_choose_us' => 'Why choose us',
  'download_apps' => 'Download apps',
  'testimonials' => 'Testimonials',
  'contact_us_page' => 'Contact us page',
  'background_colors' => 'Background colors',
  'header_section' => 'Header section',
  'Title' => 'Title',
  'Write_the_title_within_50_characters' => 'Write the title within 50 characters',
  'Ex_:_Manage_your_daily_life_on_one_platform' => 'Ex : Manage your daily life on one platform',
  'Sub Title' => 'Sub Title',
  'Write_the_sub_title_within_100_characters' => 'Write the sub title within 100 characters',
  'Ex_:_More_than_just_a_reliable_eCommerce_platform' => 'Ex : More than just a reliable eCommerce platform',
  'module_list_section' => 'Module list section',
  'Ex_:_Your_eCommerce_venture_starts_here' => 'Ex : Your eCommerce venture starts here',
  'Ex_:_Enjoy_all_services_in_one_platform' => 'Ex : Enjoy all services in one platform',
  'NB_:_All_the_modules_and_their_information_will_be_dynamically_added_from_the_module_setup_section._You_just_need_to_add_the_title_and_subtitle_of_the_Module_List_Section.' => 'NB : All the modules and their information will be dynamically added from the module setup section. You just need to add the title and subtitle of the Module List Section.',
  'Referral & Earning' => 'Referral & Earning',
  'Write_the_title_within_40_characters' => 'Write the title within 40 characters',
  'Ex_:_Earn_Point' => 'Ex : Earn Point',
  'Write_the_sub_title_within_80_characters' => 'Write the sub title within 80 characters',
  'Ex_:_By_referring_your_friend' => 'Ex : By referring your friend',
  'newsletter' => 'Newsletter',
  'Ex_:_Sign_Up_to_Our_Newsletter' => 'Ex : Sign Up to Our Newsletter',
  'Ex_:_Receive_Latest_News,_Updates_and_Many_Other_News_Every_Week' => 'Ex : Receive Latest News  Updates and Many Other News Every Week',
  'Footer_Article' => 'Footer Article',
  'Write_the_title_within_180_characters' => 'Write the title within 180 characters',
  'Ex_:_6amMart_is_a_complete_package!__It`s_time_to_empower_your_multivendor_online_business_with__powerful_features!' => 'Ex : 6amMart is a complete package!  It`s time to empower your multivendor online business with  powerful features!',
  'Browse Web Button' => 'Browse Web Button',
  'Web Link' => 'Web Link',
  'Browse Web Button Enabled for Landing Page' => 'Browse Web Button Enabled for Landing Page',
  'Browse Web Button Disabled for Landing Page' => 'Browse Web Button Disabled for Landing Page',
  'Browse Web button is enabled now everyone can use or see the button' => 'Browse Web button is enabled now everyone can use or see the button',
  'Browse Web button is disabled now no one can use or see the button' => 'Browse Web button is disabled now no one can use or see the button',
  'Notice!' => 'Notice!',
  'If you want to disable or turn off any section please leave that section empty, don’t make any changes there!' => 'If you want to disable or turn off any section please leave that section empty  don’t make any changes there!',
  'If You Want to Change Language' => 'If You Want to Change Language',
  'Change the language on tab bar and input your data again!' => 'Change the language on tab bar and input your data again!',
  'Let’s See The Changes!' => 'Let’s See The Changes!',
  'Visit landing page to see the changes you made in the settings option!' => 'Visit landing page to see the changes you made in the settings option!',
  'Visit_Now' => 'Visit Now',
  'Write_the_title_within_20_characters' => 'Write the title within 20 characters',
  'title_here...' => 'Title here...',
  'Write_the_title_within_80_characters' => 'Write the title within 80 characters',
  'sub_title_here...' => 'Sub title here...',
  'Banner' => 'Banner',
  '(size: 3:1)' => '(size: 3:1)',
  'Promotional_Banner_List' => 'Promotional Banner List',
  'Image' => 'Image',
  'Status' => 'Status',
  'By Turning ONN Promotional Banner Section' => 'By Turning ONN Promotional Banner Section',
  'By Turning OFF Promotional Banner Section' => 'By Turning OFF Promotional Banner Section',
  'Promotional banner will be enabled. You will be able to see promotional activity' => 'Promotional banner will be enabled. You will be able to see promotional activity',
  'Promotional banner will be disabled. You will be unable to see promotional activity' => 'Promotional banner will be disabled. You will be unable to see promotional activity',
  'How the Setting Works' => 'How the Setting Works',
  'Feature Title & Short Description' => 'Feature Title & Short Description',
  'Ex_:_Remarkable_Features_that_You_Can_Count' => 'Ex : Remarkable Features that You Can Count',
  'Short Description' => 'Short Description',
  'Write_the_title_within_240_characters' => 'Write the title within 240 characters',
  'Ex_:_Jam-packed_with_outstanding_features…' => 'Ex : Jam-packed with outstanding features…',
  'Save' => 'Save',
  'Ex_:_Shopping' => 'Ex : Shopping',
  'Ex_:_Best_shopping_experience' => 'Ex : Best shopping experience',
  'Features_List' => 'Features List',
  'By Turning ON ' => 'By Turning ON',
  'Feature List Section' => 'Feature List Section',
  'By Turning OFF ' => 'By Turning OFF',
  'Feature list is enabled. You can now access its features and functionality' => 'Feature list is enabled. You can now access its features and functionality',
  'Feature list will be disabled. You can enable it in the settings to access its features and functionality' => 'Feature list will be disabled. You can enable it in the settings to access its features and functionality',
  'Feature List' => 'Feature List',
  'Download User App Section Content ' => 'Download User App Section Content',
  'Download_Store_App_Section' => 'Download Store App Section',
  'Warning!' => 'Warning!',
  'Are_you_sure_you_want_to_remove_this_image' => 'Are you sure you want to remove this image',
  'Are_you_sure_you_want_to_remove_this_image.' => 'Are you sure you want to remove this image.',
  'Playstore Button' => 'Playstore Button',
  'Download Link' => 'Download Link',
  'When_disabled,_the_Play_Store_download_button_will_be_hidden_from_the_landing_page' => 'When disabled  the Play Store download button will be hidden from the landing page',
  'Want_to_enable_the_Play_Store_button_for_Store_App?' => 'Want to enable the Play Store button for Store App',
  'Want_to_disable_the_Play_Store_button_for_Store_App?' => 'Want to disable the Play Store button for Store App',
  'If_enabled,_the_Store_app_download_button_will_be_visible_on_the_Landing_page.' => 'If enabled  the Store app download button will be visible on the Landing page.',
  'If_disabled,_this_button_will_be_hidden_from_the_landing_page.' => 'If disabled  this button will be hidden from the landing page.',
  'Ex: https://play.google.com/store/apps' => 'Ex: https://play.google.com/store/apps',
  'App Store Button' => 'App Store Button',
  'When_disabled,_the_App_Store_download_button_will_be_hidden_from_the_landing_page' => 'When disabled  the App Store download button will be hidden from the landing page',
  'Want_to_enable_the_App_Store_button_for_Store_App?' => 'Want to enable the App Store button for Store App',
  'Want_to_disable_the_App_Store_button_for_Store_App?' => 'Want to disable the App Store button for Store App',
  'Ex: https://www.apple.com/app-store/' => 'Ex: https://www.apple.com/app-store/',
  'Download_Deliveryman_App_Section' => 'Download Deliveryman App Section',
  'Want_to_enable_the_Play_Store_button_for_Deliveryman_App?' => 'Want to enable the Play Store button for Deliveryman App',
  'Want_to_disable_the_Play_Store_button_for_Deliveryman_App?' => 'Want to disable the Play Store button for Deliveryman App',
  'If_enabled,_the_Deliveryman_app_download_button_will_be_visible_on_the_Landing_page.' => 'If enabled  the Deliveryman app download button will be visible on the Landing page.',
  'Want_to_enable_the_App_Store_button_for_Deliveryman_App?' => 'Want to enable the App Store button for Deliveryman App',
  'Want_to_disable_the_App_Store_button_for_Deliveryman_App?' => 'Want to disable the App Store button for Deliveryman App',
  'Admin Earn Money' => 'Admin Earn Money',
  'Download Seller App Section' => 'Download Seller App Section',
  'Download Delivery Man App Section ' => 'Download Delivery Man App Section',
  'Special Criteria List Section ' => 'Special Criteria List Section',
  'Criteria Icon/ Image' => 'Criteria Icon/ Image',
  'Icon_ratio_(1:1)_and_max_size_2_MB.' => 'Icon ratio (1:1) and max size 2 MB.',
  'Want_to_enable_this_feature?' => 'Want to enable this feature',
  'Want_to_disable_this_feature?' => 'Want to disable this feature',
  'It_will_be_available_on_the_landing_page.' => 'It will be available on the landing page.',
  'It_will_be_hidden_from_the_landing_page.' => 'It will be hidden from the landing page.',
  'Want to delete this criteria ?' => 'Want to delete this criteria ?',
  'delete_criteria' => 'Delete criteria',
  'Special Criteria' => ' Special Criteria',
  'This Criteria' => 'This Criteria',
  'This section  will be disabled. You can enable it in the settings' => 'This section  will be disabled. You can enable it in the settings',
  'This section will be enabled. You can see this section on your landing page.' => 'This section will be enabled. You can see this section on your landing page.',
  'Counter Section' => 'Counter Section',
  'By Turning ON Counter Section' => 'By Turning ON Counter Section',
  'By Turning OFF Counter Section' => 'By Turning OFF Counter Section',
  'Counter Section is enabled. You can now access its features and functionality' => 'Counter Section is enabled. You can now access its features and functionality',
  'Counter section will be disabled. You can enable it in the settings to access its features and functionality' => 'Counter section will be disabled. You can enable it in the settings to access its features and functionality',
  'Total App Download' => 'Total App Download',
  'Ex: 500' => 'Ex: 500',
  'Total Seller' => 'Total Seller',
  'Total Delivery Man' => 'Total Delivery Man',
  'Total Customer' => 'Total Customer',
  'Download User App Section Content' => 'Download User App Section Content',
  'Playstore Button Enabled for Delivery Man' => 'Playstore Button Enabled for Delivery Man',
  'Playstore Button Disabled for Delivery Man' => 'Playstore Button Disabled for Delivery Man',
  'Playstore button is enabled now everyone can use or see the button' => 'Playstore button is enabled now everyone can use or see the button',
  'Playstore button is disabled now no one can use or see the button' => 'Playstore button is disabled now no one can use or see the button',
  'App Store Button Enabled for Delivery Man' => 'App Store Button Enabled for Delivery Man',
  'App Store Button Disabled for Delivery Man' => 'App Store Button Disabled for Delivery Man',
  'App Store button is enabled now everyone can use or see the button' => 'App Store button is enabled now everyone can use or see the button',
  'App Store button is disabled now no one can use or see the button' => 'App Store button is disabled now no one can use or see the button',
  'Download Apps Section' => 'Download Apps Section',
  'Testimonial List Section' => 'Testimonial List Section',
  'Reviewer Name' => 'Reviewer Name',
  'Ex:  John Doe' => 'Ex:  John Doe',
  'Designation' => 'Designation',
  'Ex:  CTO' => 'Ex:  CTO',
  'Write_the_title_within_250_characters' => 'Write the title within 250 characters',
  'Very Good Company' => 'Very Good Company',
  'Reviewer Image *' => 'Reviewer Image *',
  'Company Logo *' => 'Company Logo *',
  'Reviews' => 'Reviews',
  'Reviewer Image' => 'Reviewer Image',
  'Company Image' => 'Company Image',
  'This review' => 'This review',
  'Want to delete this review ?' => 'Want to delete this review ?',
  'delete_review' => 'Delete review',
  'Special review' => 'Special review',
  'Ex_:_Contact_Us' => 'Ex : Contact Us',
  'Ex_:_Any_questions_or_remarks_?_Just_write_us_a_message!' => 'Ex : Any questions or remarks   Just write us a message!',
  'Office Opening & Closing' => 'Office Opening & Closing',
  'Start Time' => 'Start Time',
  'End Time' => 'End Time',
  'Start Day' => 'Start Day',
  'saturday' => 'Saturday',
  'sunday' => 'Sunday',
  'monday' => 'Monday',
  'tuesday' => 'Tuesday',
  'wednesday' => 'Wednesday',
  'thrusday' => 'Thrusday',
  'friday' => 'Friday',
  'End Day' => 'End Day',
  'Primary Color 1' => 'Primary Color 1',
  'Primary Color 2' => 'Primary Color 2',
  'Header' => 'Header',
  'Company Intro' => 'Company Intro',
  'Download User App' => 'Download User App',
  'Promotional Banners' => 'Promotional Banners',
  'Business Section' => 'Business Section',
  'fixed_Data' => 'Fixed Data',
  'Header Section' => 'Header Section',
  'Tag Line' => 'Tag Line',
  'Write_the_title_within_120_characters' => 'Write the title within 120 characters',
  'tag_line...' => 'Tag line...',
  'Icon' => 'Icon',
  '(size: 1:1)' => '(size: 1:1)',
  'If You Want to Change Text Color To Primary Color' => 'If You Want to Change Text Color To Primary Color',
  'Replace the text with ($ text $) format' => 'Replace the text with ($ text $) format',
  'Company Section' => 'Company Section',
  'Button Content' => 'Button Content',
  'Button Name' => 'Button Name',
  'Ex: Order now' => 'Ex: Order now',
  'Redirect Link' => 'Redirect Link',
  'The_button_will_direct_users_to_the_link_contained_within_this_box.' => 'The button will direct users to the link contained within this box.',
  'Write_the_title_within_60_characters' => 'Write the title within 60 characters',
  'When_disabled,_the_Play_Store_download_button_will_be_hidden_from_the_React_landing_page.' => 'When disabled  the Play Store download button will be hidden from the React landing page.',
  'Want_to_enable_the_Play_Store_button_for_User_App?' => 'Want to enable the Play Store button for User App',
  'Want_to_disable_the_Play_Store_button_for_User_App?' => 'Want to disable the Play Store button for User App',
  'If_enabled,_the_User_app_download_button_will_be_visible_on_React_Landing_page.' => 'If enabled  the User app download button will be visible on React Landing page.',
  'If_disabled,_this_button_will_be_hidden_from_the_React_landing_page.' => 'If disabled  this button will be hidden from the React landing page.',
  'When_disabled,_the_User_app_download_button_will_be_hidden_on_React_Landing_page.' => 'When disabled  the User app download button will be hidden on React Landing page.',
  'Want_to_enable_the_App_Store_button_for_User_App?' => 'Want to enable the App Store button for User App',
  'Want_to_disable_the_App_Store_button_for_User_App?' => 'Want to disable the App Store button for User App',
  'Seller Section Content' => 'Seller Section Content',
  'Write_the_title_within_30_characters' => 'Write the title within 30 characters',
  'Write_the_title_within_65_characters' => 'Write the title within 65 characters',
  'Deliveryman_Section_Content' => 'Deliveryman Section Content',
  'Banner Section' => 'Banner Section',
  'If_you_want_to_upload_one_banner_then_you_have_to_upload_it_in_6:1_ratio_otherwise_the_ratio_will_be_same_as_before.' => 'If you want to upload one banner then you have to upload it in 6:1 ratio otherwise the ratio will be same as before.',
  'banner Image' => 'Banner Image',
  'Write_the_title_within_35_characters' => 'Write the title within 35 characters',
  'Download the Seller App' => 'Download the Seller App',
  'Playstore Button Enabled for Seller' => 'Playstore Button Enabled for Seller',
  'Playstore Button Disabled for Seller' => 'Playstore Button Disabled for Seller',
  'App Store Button Enabled for Seller' => 'App Store Button Enabled for Seller',
  'App Store Button Disabled for Seller' => 'App Store Button Disabled for Seller',
  'Download the Deliveryman App' => 'Download the Deliveryman App',
  'By Turning OFF User App Button' => 'By Turning OFF User App Button',
  'User app button will be disabled. Nobody can use or see the button' => 'User app button will be disabled. Nobody can use or see the button',
  'By Turning ON User App Button' => 'By Turning ON User App Button',
  'User app button will be enabled. everyone can use or see the button' => 'User app button will be enabled. everyone can use or see the button',
  'By Turning OFF Delivery Man App Button' => 'By Turning OFF Delivery Man App Button',
  'Seller app button will be disabled. Nobody can use or see the button' => 'Seller app button will be disabled. Nobody can use or see the button',
  'By Turning ON Delivery Man App Button' => 'By Turning ON Delivery Man App Button',
  'Playstore button will be enabled. everyone can use or see the button' => 'Playstore button will be enabled. everyone can use or see the button',
  'By Turning OFF Seller App Button' => 'By Turning OFF Seller App Button',
  'By Turning ON Seller App Button' => 'By Turning ON Seller App Button',
  'Write_the_title_within_140_characters' => 'Write the title within 140 characters',
  'promotional_Banner' => 'Promotional Banner',
  '(size: 1:5)' => '(size: 1:5)',
  'Newsletter Section Content ' => 'Newsletter Section Content',
  'Write_the_title_within_100_characters' => 'Write the title within 100 characters',
  'Footer_Content' => 'Footer Content',
  'short_Description' => 'Short Description',
  'flutter_web_landing_page' => 'Flutter web landing page',
  'special_criteria' => 'Special criteria',
  'join_as' => 'Join as',
  'location_setup' => 'Location setup',
  'Module Setup' => 'Module Setup',
  'Special_Feature_List_Section ' => 'Special Feature List Section',
  'this_feature?' => 'This feature',
  'If_yes,_it_will_be_available_on_the_landing_page.' => 'If yes  it will be available on the landing page.',
  'If_yes,_it_will_be_hidden_from_the_landing_page.' => 'If yes  it will be hidden from the landing page.',
  'Want_to_delete_this_feature_?' => 'Want to delete this feature ?',
  'If_yes,_It_will_be_removed_from_this_list_and_the_landing_page.' => 'If yes  It will be removed from this list and the landing page.',
  'Join_as_a_Seller_Section' => 'Join as a Seller Section',
  'Write_the_title_within_15_characters' => 'Write the title within 15 characters',
  'button_name_here...' => 'Button name here...',
  'Button URL' => 'Button URL',
  'The_website_page_where_people_will_register_as_sellers.' => 'The website page where people will register as sellers.',
  'Join_as_a_Deliveryman_Section' => 'Join as a Deliveryman Section',
  'The_website_page_where_people_will_register_as_deliveryman.' => 'The website page where people will register as deliveryman.',
  'Join as Seller' => 'Join as Seller',
  'Join as Deliveryman' => 'Join as Deliveryman',
  'If_enabled,_the_User_app_download_button_will_be_visible_on_the_Landing_page.' => 'If enabled  the User app download button will be visible on the Landing page.',
  'about_title' => 'About title',
  'about_us_description' => 'About us description',
  'refund_policy' => 'Refund policy',
  'on' => 'On',
  'status updated!' => 'Status updated!',
  'cancellation_policy' => 'Cancellation policy',
  'upload_file' => 'Upload file',
  'upload_zip_file' => 'Upload zip file',
  'upload' => 'Upload',
  'Check how the settings works' => 'Check how the settings works',
  'Got It' => 'Got It',
  'you_want_to_delete' => 'You want to delete',
  'View Image' => 'View Image',
  'Copy Link' => 'Copy Link',
  'Download' => 'Download',
  'Copy Path' => 'Copy Path',
  'payment_gateway_setup' => 'Payment gateway setup',
  'Payment Methods' => 'Payment Methods',
  'SMS Module' => 'SMS Module',
  'Mail Config' => 'Mail Config',
  'Map APIs' => 'Map APIs',
  'Social Logins' => 'Social Logins',
  'Recaptcha' => 'Recaptcha',
  'Without configuring this section functionality will not work properly. Thus the whole system will not work as it planned' => 'Without configuring this section functionality will not work properly. Thus the whole system will not work as it planned',
  'By Turning ON Cash On Delivery Option' => 'By Turning ON Cash On Delivery Option',
  'By Turning OFF Cash On Delivery Option' => 'By Turning OFF Cash On Delivery Option',
  'Customers will not be able to select COD as a payment method during checkout. Please review your settings and enable COD if you wish to offer this payment option to customers.' => 'Customers will not be able to select COD as a payment method during checkout. Please review your settings and enable COD if you wish to offer this payment option to customers.',
  'Customers will be able to select COD as a payment method during checkout.' => 'Customers will be able to select COD as a payment method during checkout.',
  'By Turning ON Digital Payment Option' => 'By Turning ON Digital Payment Option',
  'By Turning OFF Digital Payment Option' => 'By Turning OFF Digital Payment Option',
  'Customers will not be able to select digital payment as a payment method during checkout. Please review your settings and enable digital payment if you wish to offer this payment option to customers.' => 'Customers will not be able to select digital payment as a payment method during checkout. Please review your settings and enable digital payment if you wish to offer this payment option to customers.',
  'Customers will be able to select digital payment as a payment method during checkout.' => 'Customers will be able to select digital payment as a payment method during checkout.',
  'sslcommerz' => 'Sslcommerz',
  'off' => 'Off',
  'paypal' => 'Paypal',
  'select_payment_mode' => 'Select payment mode',
  'razorpay' => 'Razorpay',
  'stripe' => 'Stripe',
  'paystack' => 'Paystack',
  'paystack_callback_warning' => 'Paystack callback warning',
  'copy_callback' => 'Copy callback',
  'senang_pay' => 'Senang pay',
  'mercadopago' => 'Mercadopago',
  'paymob_accept' => 'Paymob accept',
  'callback' => 'Callback',
  'bkash' => 'Bkash',
  'paytabs' => 'Paytabs',
  'paytm' => 'Paytm',
  'liqpay' => 'Liqpay',
  'FCM Settings' => 'FCM Settings',
  'firebase_push_notification_setup' => 'Firebase push notification setup',
  'Push Notification' => 'Push Notification',
  'Firebase Configuration' => 'Firebase Configuration',
  'Read Documentation' => 'Read Documentation',
  'Where to get this information' => 'Where to get this information',
  '*Select Module Here' => '*Select Module Here',
  'order_pending_message' => 'Order pending message',
  'By Turning ON Order ' => 'By Turning ON Order',
  'pending Message' => 'Pending Message',
  'By Turning OFF Order ' => 'By Turning OFF Order',
  'User will get a clear message to know that order is pending' => 'User will get a clear message to know that order is pending',
  'User can not get a clear message to know that order is pending or not' => 'User can not get a clear message to know that order is pending or not',
  'Write your message' => 'Write your message',
  'order_confirmation_message' => 'Order confirmation message',
  'confirmation Message' => 'Confirmation Message',
  'User will get a clear message to know that order is confirmed' => 'User will get a clear message to know that order is confirmed',
  'User can not get a clear message to know that order is confirmed or not' => 'User can not get a clear message to know that order is confirmed or not',
  'order_processing_message' => 'Order processing message',
  'processing Message' => 'Processing Message',
  'User will get a clear message to know that order is processing' => 'User will get a clear message to know that order is processing',
  'User can not get a clear message to know that order is processing or not' => 'User can not get a clear message to know that order is processing or not',
  'order_Handover_message' => 'Order Handover message',
  'Order Handover Message' => 'Order Handover Message',
  'User will get a clear message to know that order is handovered' => 'User will get a clear message to know that order is handovered',
  'User can not get a clear message to know that order is handovered or not' => 'User can not get a clear message to know that order is handovered or not',
  'order_out_for_delivery_message' => 'Order out for delivery message',
  'Out For Delivery Message' => 'Out For Delivery Message',
  'User will get a clear message to know that order is out for delivery' => 'User will get a clear message to know that order is out for delivery',
  'User can not get a clear message to know that order is out for delivery or not' => 'User can not get a clear message to know that order is out for delivery or not',
  'order_delivered_message' => 'Order delivered message',
  'delivered Message' => 'Delivered Message',
  'User will get a clear message to know that order is delivered' => 'User will get a clear message to know that order is delivered',
  'User can not get a clear message to know that order is delivered or not' => 'User can not get a clear message to know that order is delivered or not',
  'deliveryman_assign_message' => 'Deliveryman assign message',
  'Delivery Man Assigned Message' => 'Delivery Man Assigned Message',
  'User will get a clear message to know that order is assigned to delivery man' => 'User will get a clear message to know that order is assigned to delivery man',
  'User can not get a clear message to know that order is assigned to delivery man or not' => 'User can not get a clear message to know that order is assigned to delivery man or not',
  'deliveryman_delivered_message' => 'Deliveryman delivered message',
  'Delivery Man Delivered Message' => 'Delivery Man Delivered Message',
  'User will get a clear message to know that order is delivered by delivery man' => 'User will get a clear message to know that order is delivered by delivery man',
  'User can not get a clear message to know that order is delivered by delivery man or not' => 'User can not get a clear message to know that order is delivered by delivery man or not',
  'order_canceled_message' => 'Order canceled message',
  'canceled Message' => 'Canceled Message',
  'User will get a clear message to know that order is canceled' => 'User will get a clear message to know that order is canceled',
  'User can not get a clear message to know that order is canceled or not' => 'User can not get a clear message to know that order is canceled or not',
  'order_refunded_message' => 'Order refunded message',
  'Order Refund Message' => 'Order Refund Message',
  'User will get a clear message to know that order is refunded' => 'User will get a clear message to know that order is refunded',
  'User can not get a clear message to know that order is refunded or not' => 'User can not get a clear message to know that order is refunded or not',
  'refund_request_canceled_message' => 'Refund request canceled message',
  'Refund Request Cancel Message' => 'Refund Request Cancel Message',
  'User will get a clear message to know that orders refund request canceled' => 'User will get a clear message to know that orders refund request canceled',
  'User can not get a clear message to know that orders refund request canceled or not' => 'User can not get a clear message to know that orders refund request canceled or not',
  'Go to Firebase Console' => 'Go to Firebase Console',
  'Open your web browser and go to the Firebase Console' => 'Open your web browser and go to the Firebase Console',
  '(https://console.firebase.google.com/)' => '(https://console.firebase.google.com/)',
  'Select the project for which you want to configure FCM from the Firebase Console dashboard.' => 'Select the project for which you want to configure FCM from the Firebase Console dashboard.',
  'Navigate to Project Settings' => 'Navigate to Project Settings',
  'In the left-hand menu, click on the "Settings" gear icon, and then select "Project settings" from the dropdown.' => 'In the left-hand menu  click on the  Settings  gear icon  and then select  Project settings  from the dropdown.',
  'In the Project settings page, click on the "Cloud Messaging" tab from the top menu.' => 'In the Project settings page  click on the  Cloud Messaging  tab from the top menu.',
  'Obtain All The Information Asked!' => 'Obtain All The Information Asked!',
  'In the Firebase Project settings page, click on the "General" tab from the top menu.' => 'In the Firebase Project settings page  click on the  General  tab from the top menu.',
  'Under the "Your apps" section, click on the "Web" app for which you want to configure FCM.' => 'Under the  Your apps  section  click on the  Web  app for which you want to configure FCM.',
  'Then Obtain API Key, FCM Project ID, Auth Domain, Storage Bucket, Messaging Sender ID.' => 'Then Obtain API Key  FCM Project ID  Auth Domain  Storage Bucket  Messaging Sender ID.',
  'Note: Please make sure to use the obtained information securely and in accordance with Firebase and FCM documentation, terms of service, and any applicable laws and regulations.' => 'Note: Please make sure to use the obtained information securely and in accordance with Firebase and FCM documentation  terms of service  and any applicable laws and regulations.',
  'Write_a_message_in_the_Notification_Body' => 'Write a message in the Notification Body',
  'you_can_add_your_message_using_placeholders_to_include_dynamic_content._Here_are_some_examples_of_placeholders_you_can_use:' => 'You can add your message using placeholders to include dynamic content. Here are some examples of placeholders you can use:',
  'the_name_of_the_user.' => 'The name of the user.',
  'the_name_of_the_store.' => 'The name of the store.',
  'the_order_id.' => 'The order id.',
  'Please Visit the Docs to Set FCM on Mobile Apps' => 'Please Visit the Docs to Set FCM on Mobile Apps',
  'Please check the documentation below for detailed instructions on setting up your mobile app to receive Firebase Cloud Messaging (FCM) notifications.' => 'Please check the documentation below for detailed instructions on setting up your mobile app to receive Firebase Cloud Messaging (FCM) notifications.',
  'Click Here' => 'Click Here',
  'Pending Message' => 'Pending Message',
  'User can\'t get a clear message to know that order is pending or not' => 'User can t get a clear message to know that order is pending or not',
  'login_page_setup' => 'Login page setup',
  'Admin_login_page' => 'Admin login page',
  'Admin_login_url' => 'Admin login url',
  'Add_dynamic_url_to_secure_admin_login_access.' => 'Add dynamic url to secure admin login access.',
  'admin_login_url' => 'Admin login url',
  'admin_employee_login_page' => 'Admin employee login page',
  'admin_employee_login_url' => 'Admin employee login url',
  'Add_dynamic_url_to_secure_admin_employee_login_access.' => 'Add dynamic url to secure admin employee login access.',
  'store_login_page' => 'Store login page',
  'store_login_url' => 'Store login url',
  'Add_dynamic_url_to_secure_store_login_access.' => 'Add dynamic url to secure store login access.',
  'store_employee_login_page' => 'Store employee login page',
  'store_employee_login_url' => 'Store employee login url',
  'Add_dynamic_url_to_secure_store_employee_login_access.' => 'Add dynamic url to secure store employee login access.',
  'react_site_setup' => 'React site setup',
  'React Site Setup' => 'React Site Setup',
  'React license code' => 'React license code',
  'React Domain' => 'React Domain',
  'server_key' => 'Server key',
  'Ex: AAAAaBcDeFgHiJkLmNoPqRsTuVwXyZ0123456789' => 'Ex: AAAAaBcDeFgHiJkLmNoPqRsTuVwXyZ0123456789',
  'api_key' => 'Api key',
  'Ex: abcd1234efgh5678ijklmnop90qrstuvwxYZ' => 'Ex: abcd1234efgh5678ijklmnop90qrstuvwxYZ',
  'FCM Project ID' => 'FCM Project ID',
  'Ex: my-awesome-app-12345' => 'Ex: my-awesome-app-12345',
  'auth_domain' => 'Auth domain',
  'Ex: my-awesome-app.firebaseapp.com' => 'Ex: my-awesome-app.firebaseapp.com',
  'storage_bucket' => 'Storage bucket',
  'Ex: my-awesome-app.appspot.com' => 'Ex: my-awesome-app.appspot.com',
  'messaging_sender_id' => 'Messaging sender id',
  'Ex: **********' => 'Ex: **********',
  'app_id' => 'App id',
  'Ex: **********' => 'Ex: **********',
  'measurement_id' => 'Measurement id',
  'Ex: **********' => 'Ex: **********',
  'sms_gateway_setup' => 'Sms gateway setup',
  'twilio_sms' => 'Twilio sms',
  'NB : #OTP# will be replace with otp' => 'NB : #OTP# will be replace with otp',
  'sid' => 'Sid',
  'messaging_service_id' => 'Messaging service id',
  'token' => 'Token',
  'from' => 'From',
  'otp_template' => 'Otp template',
  'nexmo_sms' => 'Nexmo sms',
  'api_secret' => 'Api secret',
  '2factor_sms' => '2factor sms',
  'EX of SMS provider`s template : your OTP is XXXX here, please check.' => 'EX of SMS provider`s template : your OTP is XXXX here  please check.',
  'msg91_sms' => 'Msg91 sms',
  'NB : Keep an OTP variable in your SMS providers OTP Template.' => 'NB : Keep an OTP variable in your SMS providers OTP Template.',
  'template_id' => 'Template id',
  'authkey' => 'Authkey',
  'mail_config' => 'Mail config',
  'smtp_mail_setup' => 'Smtp mail setup',
  'Send Test Mail' => 'Send Test Mail',
  'Turn OFF' => 'Turn OFF',
  'Enabling mail configuration services will allow the system to send emails. Please ensure that you have correctly configured the SMTP settings to avoid potential issues with email delivery.' => 'Enabling mail configuration services will allow the system to send emails. Please ensure that you have correctly configured the SMTP settings to avoid potential issues with email delivery.',
  'Disabling mail configuration services will prevent the system from sending emails. Please only turn off this service if you intend to temporarily suspend email sending. Note that this may affect system functionality that relies on email communication.' => 'Disabling mail configuration services will prevent the system from sending emails. Please only turn off this service if you intend to temporarily suspend email sending. Note that this may affect system functionality that relies on email communication.',
  '*By Turning OFF mail configuration, all your mailing services will be off.' => '*By Turning OFF mail configuration  all your mailing services will be off.',
  'mailer_name' => 'Mailer name',
  'host' => 'Host',
  'Ex_:_mail.6am.one' => 'Ex : mail.6am.one',
  'driver' => 'Driver',
  'Ex : smtp' => 'Ex : smtp',
  'port' => 'Port',
  'Ex : 587' => 'Ex : 587',
  'username' => 'Username',
  'email_id' => 'Email id',
  'encryption' => 'Encryption',
  'Ex : 5+ Characters' => 'Ex : 5+ Characters',
  'Congratulations! Your SMTP mail has been setup successfully!' => 'Congratulations! Your SMTP mail has been setup successfully!',
  'Go to test mail to check that its work perfectly or not!' => 'Go to test mail to check that its work perfectly or not!',
  'Send a Test Mail to Your Email ? ' => 'Send a Test Mail to Your Email  ?',
  'A test mail will be send to your email to confirm it works perfectly.' => 'A test mail will be send to your email to confirm it works perfectly.',
  'Find SMTP Server Details' => 'Find SMTP Server Details',
  'Contact your email service provider or IT administrator to obtain the SMTP server details, such as hostname, port, username, and password.' => 'Contact your email service provider or IT administrator to obtain the SMTP server details  such as hostname  port  username  and password.',
  'Note: If you\'re not sure where to find these details, check the email provider\'s documentation or support resources for guidance.' => 'Note: If you re not sure where to find these details  check the email provider s documentation or support resources for guidance.',
  'Configure SMTP Settings' => 'Configure SMTP Settings',
  'Go to the SMTP mail setup page in the admin panel.' => 'Go to the SMTP mail setup page in the admin panel.',
  'Enter the obtained SMTP server details, including the hostname, port, username, and password.' => 'Enter the obtained SMTP server details  including the hostname  port  username  and password.',
  'Choose the appropriate encryption method (e.g., SSL, TLS) if required. Save the settings.' => 'Choose the appropriate encryption method (e.g.  SSL  TLS) if required. Save the settings.',
  'Test SMTP Connection' => 'Test SMTP Connection',
  'Click on the "Send Test Mail" button to verify the SMTP connection.' => 'Click on the  Send Test Mail  button to verify the SMTP connection.',
  'If successful, you will see a confirmation message indicating that the connection is working fine.' => 'If successful  you will see a confirmation message indicating that the connection is working fine.',
  'If not, double-check your SMTP settings and try again.' => 'If not  double-check your SMTP settings and try again.',
  'Note: If you\'re unsure about the SMTP settings, contact your email service provider or IT administrator for assistance.' => 'Note: If you re unsure about the SMTP settings  contact your email service provider or IT administrator for assistance.',
  'Enable Mail Configuration' => 'Enable Mail Configuration',
  'If the SMTP connection test is successful, you can now enable the mail configuration services by toggling the switch to "ON."' => 'If the SMTP connection test is successful  you can now enable the mail configuration services by toggling the switch to  ON.',
  'This will allow the system to send emails using the configured SMTP settings.' => 'This will allow the system to send emails using the configured SMTP settings.',
  'configuration_updated_successfully' => 'Configuration updated successfully',
  'third_party_apis' => 'Third party apis',
  'Google Map API Setup' => 'Google Map API Setup',
  'Without configuring this section map functionality will not work properly. Thus the whole
                                 system will not work as it planned' => 'Without configuring this section map functionality will not work properly. Thus the whole
                                 system will not work as it planned',
  'map_api_hint_map_api_hint_2' => 'Map api hint map api hint 2',
  'map_api_key' => 'Map api key',
  'client' => 'Client',
  'server' => 'Server',
  'Social Login Setup' => 'Social Login Setup',
  'google' => 'Google',
  'Login Turned ON ' => 'Login Turned ON',
  'Login Turned OFF ' => 'Login Turned OFF',
  'Login is now enabled. Customers will be able to sign up or log in using their social media accounts.' => 'Login is now enabled. Customers will be able to sign up or log in using their social media accounts.',
  'Login is now disabled. Customers will not be able to sign up or log in using their social media accounts. Please note that this may affect user experience and registration/login process.' => 'Login is now disabled. Customers will not be able to sign up or log in using their social media accounts. Please note that this may affect user experience and registration/login process.',
  'Credential Setup' => 'Credential Setup',
  'callback_uri' => 'Callback uri',
  'client_id' => 'Client id',
  'client_secret' => 'Client secret',
  'facebook' => 'Facebook',
  'apple' => 'Apple',
  'team_id' => 'Team id',
  'key_id' => 'Key id',
  'service_file' => 'Service file',
  '(Already Exists)' => '(Already Exists)',
  'google_api_setup_instructions' => 'Google api setup instructions',
  'go_to_the_credentials_page' => 'Go to the credentials page',
  'click' => 'Click',
  'here' => 'Here',
  'create_credentials' => 'Create credentials',
  'auth_client_id' => 'Auth client id',
  'select_the' => 'Select the',
  'web_application' => 'Web application',
  'name_your_auth_client' => 'Name your auth client',
  'add_uri' => 'Add uri',
  'authorized_redirect_uris' => 'Authorized redirect uris',
  'provide_the' => 'Provide the',
  'from_below_and_click' => 'From below and click',
  'created' => 'Created',
  'and' => 'And',
  'past_in_the_input_field_below_and' => 'Past in the input field below and',
  'facebook_api_set_instruction' => 'Facebook api set instruction',
  'goto_the_facebook_developer_page' => 'Goto the facebook developer page',
  'click_here' => 'Click here',
  'goto' => 'Goto',
  'get_started' => 'Get started',
  'from_navbar' => 'From navbar',
  'from_register_tab_press' => 'From register tab press',
  'continue' => 'Continue',
  'if_needed' => 'If needed',
  'provide_primary_email_and_press' => 'Provide primary email and press',
  'confirm_email' => 'Confirm email',
  'in_about_section_select' => 'In about section select',
  'other' => 'Other',
  'and_press' => 'And press',
  'complete_registration' => 'Complete registration',
  'create_app' => 'Create app',
  'select_an_app_type_and_press' => 'Select an app type and press',
  'complete_the_details_form_and_press' => 'Complete the details form and press',
  'form' => 'Form',
  'facebook_login' => 'Facebook login',
  'press' => 'Press',
  'set_up' => 'Set up',
  'web' => 'Web',
  'provide' => 'Provide',
  'site_url' => 'Site url',
  'base_url_of_the_site' => 'Base url of the site',
  'now_go_to' => 'Now go to',
  'setting' => 'Setting',
  'left_sidebar' => 'Left sidebar',
  'make_sure_to_check' => 'Make sure to check',
  'client_auth_login' => 'Client auth login',
  'must_on' => 'Must on',
  'valid_auth_redirect_uris' => 'Valid auth redirect uris',
  'from_left_sidebar' => 'From left sidebar',
  'basic' => 'Basic',
  'fill_the_form_and_press' => 'Fill the form and press',
  'now_copy' => 'Now copy',
  'apple_api_set_instruction' => 'Apple api set instruction',
  'Go to Apple Developer page' => 'Go to Apple Developer page',
  'Here in top left corner you can see the' => 'Here in top left corner you can see the',
  'Team ID' => 'Team ID',
  '[Apple_Deveveloper_Account_Name - Team_ID]' => '[Apple Deveveloper Account Name - Team ID]',
  'Click Plus icon -> select App IDs -> click on Continue' => 'Click Plus icon -  select App IDs -  click on Continue',
  'Put a description and also identifier (identifier that used for app) and this is the' => 'Put a description and also identifier (identifier that used for app) and this is the',
  'Client ID' => 'Client ID',
  'Click Continue and Download the file in device named AuthKey_ID.p8 (Store it safely and it is used for push notification)' => 'Click Continue and Download the file in device named AuthKey ID.p8 (Store it safely and it is used for push notification)',
  'Again click Plus icon -> select Service IDs -> click on Continue' => 'Again click Plus icon -  select Service IDs -  click on Continue',
  'Push a description and also identifier and Continue' => 'Push a description and also identifier and Continue',
  'Download the file in device named' => 'Download the file in device named',
  'AuthKey_KeyID.p8' => 'AuthKey KeyID.p8',
  '[This is the Service Key ID file and also after AuthKey_ that is the Key ID]' => '[This is the Service Key ID file and also after AuthKey  that is the Key ID]',
  'twitter_api_set_up_instructions' => 'Twitter api set up instructions',
  'instruction_will_be_available_very_soon' => 'Instruction will be available very soon',
  'Copied to the clipboard' => 'Copied to the clipboard',
  'File Exists' => 'File Exists',
  'File not found' => 'File not found',
  'reCaptcha Setup' => 'ReCaptcha Setup',
  'reCaptcha_credentials_setup' => 'ReCaptcha credentials setup',
  'Turn ON' => 'Turn ON',
  'reCAPTCHA is now enabled for added security. Users may be prompted to complete a reCAPTCHA challenge to verify their human identity and protect against spam and malicious activity.' => 'ReCAPTCHA is now enabled for added security. Users may be prompted to complete a reCAPTCHA challenge to verify their human identity and protect against spam and malicious activity.',
  'Disabling reCAPTCHA may leave your website vulnerable to spam and malicious activity and suspects that a user may be a bot. It is highly recommended to keep reCAPTCHA enabled to ensure the security and integrity of your website.' => 'Disabling reCAPTCHA may leave your website vulnerable to spam and malicious activity and suspects that a user may be a bot. It is highly recommended to keep reCAPTCHA enabled to ensure the security and integrity of your website.',
  'Site Key' => 'Site Key',
  'Secret Key' => 'Secret Key',
  'Go to the Credentials page' => 'Go to the Credentials page',
  'Click' => 'Click',
  'Add a ' => 'Add a',
  'label' => 'Label',
  '(Ex: Test Label)' => '(Ex: Test Label)',
  'Select reCAPTCHA v2 as ' => 'Select reCAPTCHA v2 as',
  'reCAPTCHA Type' => 'ReCAPTCHA Type',
  'Sub type: I\'m not a robot Checkbox' => 'Sub type: I m not a robot Checkbox',
  'domain' => 'Domain',
  '(For ex: demo.6amtech.com)' => '(For ex: demo.6amtech.com)',
  'Check in ' => 'Check in',
  'Accept the reCAPTCHA Terms of Service' => 'Accept the reCAPTCHA Terms of Service',
  'Press' => 'Press',
  'Copy' => 'Copy',
  'Site' => 'Site',
  'Key' => 'Key',
  'Secret' => 'Secret',
  'paste in the input filed below and' => 'Paste in the input filed below and',
  'Email Templates' => 'Email Templates',
  'Admin_Mail_Templates' => 'Admin Mail Templates',
  'Store_Mail_Templates' => 'Store Mail Templates',
  'Delivery_Man_Mail_Templates' => 'Delivery Man Mail Templates',
  'Customer_Mail_Templates' => 'Customer Mail Templates',
  'Forgot Password' => 'Forgot Password',
  'New Store Registration' => 'New Store Registration',
  'New Delivery Man Registration' => 'New Delivery Man Registration',
  'Campaign Join Request' => 'Campaign Join Request',
  'Send_Mail_On_‘Forgot_Password’?' => 'Send Mail On ‘Forgot Password’',
  'If_a_user_clicks_‘Forgot_Password’_during_login,_an_automated_email_will_be_sent_to_the_admin.' => 'If a user clicks ‘Forgot Password’ during login  an automated email will be sent to the admin.',
  'Want_to_enable_Forget_Password_mail?' => 'Want to enable Forget Password mail',
  'Want_to_disable_Forget_Password_mail?' => 'Want to disable Forget Password mail',
  'If_enabled,_admin_will_receive_an_email_when_a_user_click_on_‘Forgot_Password.’' => 'If enabled  admin will receive an email when a user click on ‘Forgot Password.’',
  'If_disabled,_admin_will_not_receive_an_email_when_a_user_click_on_‘Forgot_Password.’' => 'If disabled  admin will not receive an email when a user click on ‘Forgot Password.’',
  'Thanks_&_Regards' => 'Thanks & Regards',
  'Privacy_Policy' => 'Privacy Policy',
  'Refund_Policy' => 'Refund Policy',
  'Cancelation_Policy' => 'Cancelation Policy',
  'Contact_us' => 'Contact us',
  'Read Instructions' => 'Read Instructions',
  'Header Content' => 'Header Content',
  'Main Title' => 'Main Title',
  'Mail Body Message' => 'Mail Body Message',
  'Footer Content' => 'Footer Content',
  'Section Text' => 'Section Text',
  'Please_contact_us_for_any_queries;_we’re_always_happy_to_help.' => 'Please contact us for any queries  we’re always happy to help.',
  'Page Links' => 'Page Links',
  'Privacy Policy' => 'Privacy Policy',
  'Contact Us' => 'Contact Us',
  'Social Media Links' => 'Social Media Links',
  'Copyright Content' => 'Copyright Content',
  'Ex:_Copyright_2023_6amMart._All_right_reserved' => 'Ex: Copyright 2023 6amMart. All right reserved',
  'Want to disable Place Order' => 'Want to disable Place Order',
  'User will not get a confirmation email when they placed a order.' => 'User will not get a confirmation email when they placed a order.',
  'Want to enable Place Order' => 'Want to enable Place Order',
  'User will get a confirmation email when they placed a order.' => 'User will get a confirmation email when they placed a order.',
  'Select_Theme' => 'Select Theme',
  'Choose_a_related_email_template_theme_for_the_purpose_for_which_you_are_creating_the_email.' => 'Choose a related email template theme for the purpose for which you are creating the email.',
  'Choose_Logo' => 'Choose Logo',
  'Upload_your_company_logo_in_1:1_format._This_will_show_above_the_Main_Title_of_the_email.' => 'Upload your company logo in 1:1 format. This will show above the Main Title of the email.',
  'Write_a_Title' => 'Write a Title',
  'Give_your_email_a_‘Catchy_Title’_to_help_the_reader_understand_easily.' => 'Give your email a ‘Catchy Title’ to help the reader understand easily.',
  'Write_a_message_in_the_Email_Body' => 'Write a message in the Email Body',
  'the_name_of_the_delivery_person.' => 'The name of the delivery person.',
  'the_transaction_id.' => 'The transaction id.',
  'Add_Button_&_Link' => 'Add Button & Link',
  'Specify_the_text_and_URL_for_the_button_that_you_want_to_include_in_your_email.' => 'Specify the text and URL for the button that you want to include in your email.',
  'Change_Banner_Image_if_needed' => 'Change Banner Image if needed',
  'Choose_the_relevant_banner_image_for_the_email_theme_you_use_for_this_mail.' => 'Choose the relevant banner image for the email theme you use for this mail.',
  'Add_Content_to_Email_Footer' => 'Add Content to Email Footer',
  'Write_text_on_the_footer_section_of_the_email,_and_choose_important_page_links_and_social_media_links.' => 'Write text on the footer section of the email  and choose important page links and social media links.',
  'Create_a_copyright_notice' => 'Create a copyright notice',
  'Include_a_copyright_notice_at_the_bottom_of_your_email_to_protect_your_content.' => 'Include a copyright notice at the bottom of your email to protect your content.',
  'Save_and_publish' => 'Save and publish',
  'Once_you\'ve_set_up_all_the_elements_of_your_email_template,_save_and_publish_it_for_use.' => 'Once you ve set up all the elements of your email template  save and publish it for use.',
  'Got_It' => 'Got It',
  'Receive_Mail_On_‘New_Store_Registration’?' => 'Receive Mail On ‘New Store Registration’',
  'If_a_store_registers_from_the_customer_website_or_app_or_store_app,_admin_will_receive_an_automated_email.' => 'If a store registers from the customer website or app or store app  admin will receive an automated email.',
  'Want_to_enable_Store_Registration_mail?' => 'Want to enable Store Registration mail',
  'Want_to_disable_Store_Registration_mail?' => 'Want to disable Store Registration mail',
  'If_enabled,_the_admin_will_get_an_automated_email_when_a_store_registers.' => 'If enabled  the admin will get an automated email when a store registers.',
  'If_disabled,_the_admin_will_not_get_an_automated_email_when_a_store_registers.' => 'If disabled  the admin will not get an automated email when a store registers.',
  'Logo' => 'Logo',
  'Banner image' => 'Banner image',
  'Receive_Mail_On_‘New_Deliveryman_Registration’?' => 'Receive Mail On ‘New Deliveryman Registration’',
  'If_Deliveryman_registers_from_the_customer_App_or_Website_or_Deliveryman_App,_Admin_receive_an_automated_email.' => 'If Deliveryman registers from the customer App or Website or Deliveryman App  Admin receive an automated email.',
  'Want_to_enable_Delivery_Man_Registration_mail?' => 'Want to enable Delivery Man Registration mail',
  'Want_to_disable_Delivery_Man_Registration_mail?' => 'Want to disable Delivery Man Registration mail',
  'If_enabled,_the_admin_will_get_an_automated_email_when_a_deliveryman_registers.' => 'If enabled  the admin will get an automated email when a deliveryman registers.',
  'If_disabled,_the_admin_will_not_get_an_automated_email_when_a_deliveryman_registers.' => 'If disabled  the admin will not get an automated email when a deliveryman registers.',
  'Please_contact_us_for_any_queries,_we’re_always_happy_to_help.' => 'Please contact us for any queries  we’re always happy to help.',
  'Copyright 2023 6ammart. All right reserved' => 'Copyright 2023 6ammart. All right reserved',
  'Send Mail on Withdraw Request ?' => 'Send Mail on Withdraw Request ?',
  'If_a_store_requests_for_a_withdrawal,_admin_will_receive_an_automated_email.' => 'If a store requests for a withdrawal  admin will receive an automated email.',
  'Want_to_enable_Withdraw_Request_mail?' => 'Want to enable Withdraw Request mail',
  'Want_to_disable_Withdraw_Request_mail?' => 'Want to disable Withdraw Request mail',
  'If_enabled,_admin_will_receive_an_email_when_a_store_requests_a_withdrawal.' => 'If enabled  admin will receive an email when a store requests a withdrawal.',
  'If_disabled,_admin_will_not_receive_an_email_when_a_store_requests_a_withdrawal.' => 'If disabled  admin will not receive an email when a store requests a withdrawal.',
  'Receive_Mail_on_‘Campaign_Join_Request’?' => 'Receive Mail on ‘Campaign Join Request’',
  'If_a_store_requests_to_join_campaign_an_automated_email_will_be_sent_to_the_admin.' => 'If a store requests to join campaign an automated email will be sent to the admin.',
  'Want_to_enable_Campaign_Request_mail?' => 'Want to enable Campaign Request mail',
  'Want_to_disable_Campaign_Request_mail?' => 'Want to disable Campaign Request mail',
  'If_enabled,_the_admin_will_receive_a_mail_when_a_store_requests_to_join_a_campaign.' => 'If enabled  the admin will receive a mail when a store requests to join a campaign.',
  'If_disabled,_the_admin_will_not_receive_mail_when_a_store_requests_to_join_a_campaign.' => 'If disabled  the admin will not receive mail when a store requests to join a campaign.',
  'Receive_Mail_on_‘Refund_Request’?' => 'Receive Mail on ‘Refund Request’',
  'If_a_customer_requests_a_refund,_the_admin_will_receive_an_automated_email_on_the_customer`s_Refund_Request.' => 'If a customer requests a refund  the admin will receive an automated email on the customer`s Refund Request.',
  'Want_to_enable_Refund_Request_mail?' => 'Want to enable Refund Request mail',
  'Want_to_disable_Refund_Request_mail?' => 'Want to disable Refund Request mail',
  'If_enabled,_the_admin_will_receive_an_email_when_a_customer_requests_a_refund_from_the_customer_app_or_website.' => 'If enabled  the admin will receive an email when a customer requests a refund from the customer app or website.',
  'If_disabled,_the_admin_will_not_receive_an_email_when_a_customer_requests_a_refund_from_the_customer_app_or_website. ' => 'If disabled  the admin will not receive an email when a customer requests a refund from the customer app or website.',
  'New_Store_Approval' => 'New Store Approval',
  'New_Store_Rejection' => 'New Store Rejection',
  'Withdraw_Approval' => 'Withdraw Approval',
  'Withdraw_Rejection' => 'Withdraw Rejection',
  'Campaign_Join_Approval' => 'Campaign Join Approval',
  'Campaign_Join_Rejection' => 'Campaign Join Rejection',
  'Send Mail on Store Registration ?' => 'Send Mail on Store Registration ?',
  'If_a_Store_registers_from_the_Customer_app_or_Website,_Admin_Landing_Page_or_Store_app,_they_will_get_a_confirmation_email.' => 'If a Store registers from the Customer app or Website  Admin Landing Page or Store app  they will get a confirmation email.',
  'If_enabled,_stores_will_get_a_registration_confirmation_email_when_they_register.' => 'If enabled  stores will get a registration confirmation email when they register.',
  'If_disabled,_stores_will_not_get_a_registration_confirmation_email_when_a_store_registers.' => 'If disabled  stores will not get a registration confirmation email when a store registers.',
  'Send_Mail_on_New_Store_Approval?' => 'Send Mail on New Store Approval',
  'If_Admin_accepts_a_Store’s_self-registration,_the_store_will_get_an_automatic_approval_mail_from_the_system.' => 'If Admin accepts a Store’s self-registration  the store will get an automatic approval mail from the system.',
  'Want_to_enable_Store_approve_mail?' => 'Want to enable Store approve mail',
  'Want_to_disable_Store_approve_mail?' => 'Want to disable Store approve mail',
  'If_enabled,_Users_will_get_a_confirmation_email_when_the_Admin_approves_the_registration.' => 'If enabled  Users will get a confirmation email when the Admin approves the registration.',
  'If_disabled,_Users_will_not_get_a_registration_approval_email.' => 'If disabled  Users will not get a registration approval email.',
  'Main_Title_or_Subject_of_the_Mail' => 'Main Title or Subject of the Mail',
  'Send_Mail_on_‘New_Store_Rejection’?' => 'Send Mail on ‘New Store Rejection’',
  'If_Admin_rejects_a_Store’s_self-registration,_the_store_will_get_an_automatic_disapproval_mail_from_the_system.' => 'If Admin rejects a Store’s self-registration  the store will get an automatic disapproval mail from the system.',
  'Want_to_enable_Store_deny_mail?' => 'Want to enable Store deny mail',
  'Want_to_disable_Store_deny_mail?' => 'Want to disable Store deny mail',
  'If_enabled,_Users_will_receive_a_confirmation_email_when_the_Admin_rejects_their_registration_request.' => 'If enabled  Users will receive a confirmation email when the Admin rejects their registration request.',
  'If_disabled,__Users_will_not_get_a_registration_rejection_mail.' => 'If disabled   Users will not get a registration rejection mail.',
  'Send_Mail_On_Withdraw_approve?' => 'Send Mail On Withdraw approve',
  'If_a_Store_requests_for_a_withdrawal_and_Admin_approves_it,_the_Store_will_get_an_automated_Withdraw_Approval_email_from_the_system' => 'If a Store requests for a withdrawal and Admin approves it  the Store will get an automated Withdraw Approval email from the system',
  'Want_to_enable_Withdraw_approve_mail?' => 'Want to enable Withdraw approve mail',
  'Want_to_disable_Withdraw_approve_mail?' => 'Want to disable Withdraw approve mail',
  'If_enabled,_Stores_will_receive_an_approval_mail_for_requesting_a_withdrawal.' => 'If enabled  Stores will receive an approval mail for requesting a withdrawal.',
  'If_disabled,_Stores_will_not_receive_any_Withdraw_Approval_mail.' => 'If disabled  Stores will not receive any Withdraw Approval mail.',
  'Hi_Sabrina,' => 'Hi Sabrina',
  'Send_Mail_on_‘Withdraw_Rejection’?' => 'Send Mail on ‘Withdraw Rejection’',
  'If_a_Store_requests_for_a_withdrawal_and_Admin_rejects_it,_the_Store_will_get_an_automated_Withdraw_Rejection_email_from_the_system.' => 'If a Store requests for a withdrawal and Admin rejects it  the Store will get an automated Withdraw Rejection email from the system.',
  'Want_to_enable_Withdraw_deny_mail?' => 'Want to enable Withdraw deny mail',
  'Want_to_disable_Withdraw_deny_mail?' => 'Want to disable Withdraw deny mail',
  'If_enabled,_Stores_will_not_receive_any_Withdrawal_Rejection_mail.' => 'If enabled  Stores will not receive any Withdrawal Rejection mail.',
  'If_disabled,_Stores_will_receive_an_automated_mail_from_the_system_when_the_Admin_Rejects_their_Withdraw_Request.' => 'If disabled  Stores will receive an automated mail from the system when the Admin Rejects their Withdraw Request.',
  'Send Mail on Campaign Request ?' => 'Send Mail on Campaign Request ?',
  'If_a_Store_requests_to_join_a_campaign,_they_will_receive_an_automated_mail_for_successful_registration.' => 'If a Store requests to join a campaign  they will receive an automated mail for successful registration.',
  'If_enabled,_Stores_will_receive_an_automated_confirmation_mail_that_their_join_request_is_successful.' => 'If enabled  Stores will receive an automated confirmation mail that their join request is successful.',
  'If_disabled,_Stores_will_not_receive_any_confirmation_mail_on_campaign_join_request.' => 'If disabled  Stores will not receive any confirmation mail on campaign join request.',
  'Send_Mail_on_‘Campaign_Join_Approval’?' => 'Send Mail on ‘Campaign Join Approval’',
  'If_a_Store_requests_to_join_a_Campaign_and_Admin_approves_their_joining_request,_they_get_an_automated_Approval_email_from_the_system.' => 'If a Store requests to join a Campaign and Admin approves their joining request  they get an automated Approval email from the system.',
  'Want_to_enable_Campaign_Approve_mail?' => 'Want to enable Campaign Approve mail',
  'Want_to_disable_Campaign_Approve_mail?' => 'Want to disable Campaign Approve mail',
  'If_enabled,_Stores_will_receive_an_email_when_Admin_approves_their_Campaign_Join_Request.' => 'If enabled  Stores will receive an email when Admin approves their Campaign Join Request.',
  'Send_Mail_on_‘Campaign_Join_Rejection’?' => 'Send Mail on ‘Campaign Join Rejection’',
  'If_a_Store_requests_to_join_a_Campaign_and_Admin_rejects_their_joining_request,_they_will_get_an_automated_Rejection_email_from_the_system.' => 'If a Store requests to join a Campaign and Admin rejects their joining request  they will get an automated Rejection email from the system.',
  'Want_to_enable_Campaign_deny_mail?' => 'Want to enable Campaign deny mail',
  'Want_to_disable_Campaign_deny_mail?' => 'Want to disable Campaign deny mail',
  'If_enabled,_Stores_will_receive_an_email_on_campaign_joining_Rejection.' => 'If enabled  Stores will receive an email on campaign joining Rejection.',
  'If_disabled,_Stores_will_not_receive_any_email_on_campaign_joining_Rejection.' => 'If disabled  Stores will not receive any email on campaign joining Rejection.',
  'New_Deliveryman_Registration' => 'New Deliveryman Registration',
  'New_Deliveryman_Approval' => 'New Deliveryman Approval',
  'New_Deliveryman_Rejection' => 'New Deliveryman Rejection',
  'Account_Suspension' => 'Account Suspension',
  'Cash_Collection' => 'Cash Collection',
  'Forgot_Password' => 'Forgot Password',
  'Send_Mail_on_New_Deliveryman_Registration?' => 'Send Mail on New Deliveryman Registration',
  'If_a_Deliveryman_registers_from_the_Customer_app_or_Website,_Admin_Landing_Page_or_Store_app,_they_will_get_a_Registration_Confirmation_email.' => 'If a Deliveryman registers from the Customer app or Website  Admin Landing Page or Store app  they will get a Registration Confirmation email.',
  'If_enabled,_Deliverymen_will_receive_an_automated_mail_from_the_system_when_their_registration_is_successful.' => 'If enabled  Deliverymen will receive an automated mail from the system when their registration is successful.',
  'If_disabled,_Deliverymen_will_not_receive_any_registration_confirmation_email.' => 'If disabled  Deliverymen will not receive any registration confirmation email.',
  'Send_Mail_On_Delivery_Man_approve?' => 'Send Mail On Delivery Man approve',
  'If_Admin_accepts_a_Deliveryman’s_self-registration,_the_Deliveryman_will_get_an_automatic_approval_mail.' => 'If Admin accepts a Deliveryman’s self-registration  the Deliveryman will get an automatic approval mail.',
  'Want_to_enable_Delivery_Man_approve_mail?' => 'Want to enable Delivery Man approve mail',
  'Want_to_disable_Delivery_Man_approve_mail?' => 'Want to disable Delivery Man approve mail',
  'If_enabled,_Deliverymen_will_get_a_confirmation_email_when_registration_is_approved_by_the_Admin.' => 'If enabled  Deliverymen will get a confirmation email when registration is approved by the Admin.',
  'If_disabled,_Deliverymen_will_not_get_a_registration_approval_email.' => 'If disabled  Deliverymen will not get a registration approval email.',
  'Send_Mail_on_New_Deliveryman_Rejection?' => 'Send Mail on New Deliveryman Rejection',
  'If_Admin_rejects_a_Deliveryman’s_self-registration,_the_Deliveryman_will_get_an_automatic_rejection_mail.' => 'If Admin rejects a Deliveryman’s self-registration  the Deliveryman will get an automatic rejection mail.',
  'Want_to_enable_Delivery_Man_deny_mail?' => 'Want to enable Delivery Man deny mail',
  'Want_to_disable_Delivery_Man_deny_mail?' => 'Want to disable Delivery Man deny mail',
  'If_enabled,__Users_will_receive_an_email_when_the_admin_rejects_their_registration_request.' => 'If enabled   Users will receive an email when the admin rejects their registration request.',
  'If_disabled,_Users_will_not_receive_any_email_upon_rejection_for_registration.' => 'If disabled  Users will not receive any email upon rejection for registration.',
  'Send_Mail_On_Deliveryman’s_‘Account_Suspension’?' => 'Send Mail On Deliveryman’s ‘Account Suspension’',
  'If_Store/Admin_wants,_they_can_suspend_a_Deliveryman’s_account._If_a_Store_or_Admin_suspends_a_Deliveryman’s_account,_he_will_receive_an_automated_email.' => 'If Store/Admin wants  they can suspend a Deliveryman’s account. If a Store or Admin suspends a Deliveryman’s account  he will receive an automated email.',
  'Want_to_enable_Suspend_mail?' => 'Want to enable Suspend mail',
  'Want_to_disable_Suspend_mail?' => 'Want to disable Suspend mail',
  'If_enabled,_deliverymen_will_receive_an_email_for_account_suspension.' => 'If enabled  deliverymen will receive an email for account suspension.',
  'If_disabled,_deliverymen_will_not_receive_an_email_for_account_suspension.' => 'If disabled  deliverymen will not receive an email for account suspension.',
  'Send_Mail_on_‘Cash_Collection’?' => 'Send Mail on ‘Cash Collection’',
  'If_Admin_or_Store_collects_cash_from_a_Deliveryman,_he_will_receive_an_automated_email_from_the_system_showing_how_much_cash_is_collected.' => 'If Admin or Store collects cash from a Deliveryman  he will receive an automated email from the system showing how much cash is collected.',
  'Want_to_enable_cash_collect_mail?' => 'Want to enable cash collect mail',
  'Want_to_disable_cash_collect_mail?' => 'Want to disable cash collect mail',
  'If_enabled,_the_Deliveryman_will_receive_an_email_after_the_Admin/Store_collects_cash_from_him.' => 'If enabled  the Deliveryman will receive an email after the Admin/Store collects cash from him.',
  'If_disabled,_the_Deliveryman_will_not_receive_any_email_on_Cash_Collection.' => 'If disabled  the Deliveryman will not receive any email on Cash Collection.',
  'If_a_Deliveryman_tap_on_‘Forgot_Password’_during_login,_an_automated_email_will_be_sent_from_the_system_with_a_Reset_Password_Link.' => 'If a Deliveryman tap on ‘Forgot Password’ during login  an automated email will be sent from the system with a Reset Password Link.',
  'If_enabled,_the_Deliveryman_will_receive_an_automated_email_with_a_Reset_Password_link.' => 'If enabled  the Deliveryman will receive an automated email with a Reset Password link.',
  'If_disabled,_the_Deliveryman_will_not_receive_any_for_password_reset.' => 'If disabled  the Deliveryman will not receive any for password reset.',
  'New_Customer_Registration' => 'New Customer Registration',
  'Registration OTP' => 'Registration OTP',
  'Login OTP' => 'Login OTP',
  'Delivery_Verification' => 'Delivery Verification',
  'Order_Placement' => 'Order Placement',
  'Refund_Request_Rejected' => 'Refund Request Rejected',
  'Fund_Add' => 'Fund Add',
  'Receive_Mail_On_‘New_Customer_Registration’?' => 'Receive Mail On ‘New Customer Registration’',
  'If_a_user_registers_or_sign_up_from_the_Customer_App_or_Website,_they_will_receive_an_automated_confirmation.' => 'If a user registers or sign up from the Customer App or Website  they will receive an automated confirmation.',
  'Want_to_enable_User_Registration_mail?' => 'Want to enable User Registration mail',
  'Want_to_disable_User_Registration_mail?' => 'Want to disable User Registration mail',
  'If_enabled,_customers_will_receive_a_confirmation_email_that_their_registration_was_successful.' => 'If enabled  customers will receive a confirmation email that their registration was successful.',
  'If_disabled,_customers_will_receive_a_registration_confirmation_email.' => 'If disabled  customers will receive a registration confirmation email.',
  'Send_Mail_On_‘Registration_OTP’?' => 'Send Mail On ‘Registration OTP’',
  'Customers_will_receive_an_automated_email_with_an_OTP_to_confirm_their_registration.' => 'Customers will receive an automated email with an OTP to confirm their registration.',
  'If_enabled,_Customers_will_receive_OTP_in_their_mail_to_confirm_registration.' => 'If enabled  Customers will receive OTP in their mail to confirm registration.',
  'If_disabled,_Customers_will_not_receive_any_email_on_registration_OTP.' => 'If disabled  Customers will not receive any email on registration OTP.',
  'Send_Mail_On_‘Login_OTP’?' => 'Send Mail On ‘Login OTP’',
  'Customers_will_receive_an_OTP_every_time_they_log_in_to_their_account_via_the_Customer_App_or_Website.' => 'Customers will receive an OTP every time they log in to their account via the Customer App or Website.',
  'Want_to_enable_User_login_mail?' => 'Want to enable User login mail',
  'Want_to_disable_User_login_mail?' => 'Want to disable User login mail',
  'If_enabled,_customers_will_receive_a_login_OTP_email_every_time_they_log_in_to_their_account.' => 'If enabled  customers will receive a login OTP email every time they log in to their account.',
  'If_disabled,_customers_will_not_receive_any_OTP_email_during_Login.' => 'If disabled  customers will not receive any OTP email during Login.',
  'Send_Mail_On_‘Delivery_Verification’?' => 'Send Mail On ‘Delivery Verification’',
  'Customers_will_receive_a_Delivery_Verification_code_via_email_during_delivery._The_Customer_then_gives_the_code_to_the_Deliveryman_to_confirm_delivery.' => 'Customers will receive a Delivery Verification code via email during delivery. The Customer then gives the code to the Deliveryman to confirm delivery.',
  'Want_to_enable_Order_Verification_mail?' => 'Want to enable Order Verification mail',
  'Want_to_disable_Order_Verification_mail?' => 'Want to disable Order Verification mail',
  'If_enabled,_Customers_will_receive_a_Verification_code_via_mail_during_delivery_and_Deliveryman_can_verify_the_order_with_the_given_code.' => 'If enabled  Customers will receive a Verification code via mail during delivery and Deliveryman can verify the order with the given code.',
  'If_disabled,_Customers_will_not_receive_any_Verification_code_via_mail_for_delivery_verification.' => 'If disabled  Customers will not receive any Verification code via mail for delivery verification.',
  'Send_Mail_On_‘Order_Placement’?' => 'Send Mail On ‘Order Placement’',
  'Customers_will_receive_an_automated_email_after_a_successful_order_placement.' => 'Customers will receive an automated email after a successful order placement.',
  'Want_to_enable_Place_Order_mail?' => 'Want to enable Place Order mail',
  'Want_to_disable_Place_Order_mail?' => 'Want to disable Place Order mail',
  'If_enabled,_customers_will_get_an_automatic_confirmation_mail_for_successful_Order_Placement_with_an_invoice.' => 'If enabled  customers will get an automatic confirmation mail for successful Order Placement with an invoice.',
  'If_disabled,_customers_will_NOT_get_any_Order_Placement_email.' => 'If disabled  customers will NOT get any Order Placement email.',
  'Order_Info' => 'Order Info',
  'Order_Summary' => 'Order Summary',
  'Order' => 'Order',
  'Delivery_Address' => 'Delivery Address',
  'Product' => 'Product',
  'Price' => 'Price',
  'Send Mail on Refund Order ?' => 'Send Mail on Refund Order ?',
  'Customers_will_get_an_automated_email_when_they_receive_a_refund_to_their_wallet_from_Admin_with_refund_details.' => 'Customers will get an automated email when they receive a refund to their wallet from Admin with refund details.',
  'Want_to_enable_Refund_Order_mail?' => 'Want to enable Refund Order mail',
  'Want_to_disable_Refund_Order_mail?' => 'Want to disable Refund Order mail',
  'If_enabled,_Customers_will_get_an_automated_email_when_they_receive_a_refund.' => 'If enabled  Customers will get an automated email when they receive a refund.',
  'If_disabled,_Customers_will_not_receive_any_mail_on_Refund_Orders.' => 'If disabled  Customers will not receive any mail on Refund Orders.',
  'Want to disable Refund Order' => 'Want to disable Refund Order',
  'Want to enable Refund Order' => 'Want to enable Refund Order',
  'If_a_Customer_clicks_on_‘Forgot_Password’_during_login,_an_automated_email_will_be_sent_with_a_Reset_Password_Link.' => 'If a Customer clicks on ‘Forgot Password’ during login  an automated email will be sent with a Reset Password Link.',
  'If_enabled,_the_Customer_will_receive_an_automated_email_with_a_Reset_Password_link.' => 'If enabled  the Customer will receive an automated email with a Reset Password link.',
  'Send_Mail_On_‘Refund_Request_Rejected’?' => 'Send Mail On ‘Refund Request Rejected’',
  'Customers_will_receive_an_automated_mail_from_the_system_if_the_Admin_rejects_their_Refund_Request.' => 'Customers will receive an automated mail from the system if the Admin rejects their Refund Request.',
  'Want_to_enable_Refund_Request_Denied_mail?' => 'Want to enable Refund Request Denied mail',
  'Want_to_disable_Refund_Request_Denied_mail?' => 'Want to disable Refund Request Denied mail',
  'If_enabled,_Customers_will_receive_a_mail_when_Admin_rejects_their_Refund_Request.' => 'If enabled  Customers will receive a mail when Admin rejects their Refund Request.',
  'If_disabled,_Customers_will_not_receive_any_mail_for_Refund_Request_rejection.' => 'If disabled  Customers will not receive any mail for Refund Request rejection.',
  'Send_Mail_On_‘Fund_Add’?' => 'Send Mail On ‘Fund Add’',
  'Customers_will_receive_an_automated_mail_from_the_system_when_Admin_add_fund_to_their_Wallet.' => 'Customers will receive an automated mail from the system when Admin add fund to their Wallet.',
  'Want_to_enable_add_fund_mail?' => 'Want to enable add fund mail',
  'Want_to_disable_add_fund_mail?' => 'Want to disable add fund mail',
  'If_enabled,_customers_will_receive_an_email_when_Admin_adds_funds_to_their_wallet.' => 'If enabled  customers will receive an email when Admin adds funds to their wallet.',
  'If_disabled,_Customers_will_not_receive_an_email_on_Added_Funds.' => 'If disabled  Customers will not receive an email on Added Funds.',
  'User App Version Control' => 'User App Version Control',
  'For android' => 'For android',
  'Minimum_User_App_Version' => 'Minimum User App Version',
  'android' => 'Android',
  'The_minimum_user_app_version_required_for_the_app_functionality.' => 'The minimum user app version required for the app functionality.',
  'app_minimum_version' => 'App minimum version',
  'Download_URL_for_User_App' => 'Download URL for User App',
  'Users_will_download_the_latest_user_app_version_using_this_URL.' => 'Users will download the latest user app version using this URL.',
  'app_url' => 'App url',
  'For iOS' => 'For iOS',
  'ios' => 'Ios',
  'Store_App_Version_Control' => 'Store App Version Control',
  'Minimum_Store_App_Version_for_store' => 'Minimum Store App Version for store',
  'The_minimum_store_app_version_required_for_the_app_functionality.' => 'The minimum store app version required for the app functionality.',
  'Download_URL_for_Store_App_for_store' => 'Download URL for Store App for store',
  'Users_will_download_the_latest_store_app_using_this_URL.' => 'Users will download the latest store app using this URL.',
  'Download_Url' => 'Download Url',
  'Minimum_Store_App_Version' => 'Minimum Store App Version',
  'Download_URL_for_Store_App' => 'Download URL for Store App',
  'Users_will_download_the_latest_store_app_version_using_this_URL.' => 'Users will download the latest store app version using this URL.',
  'Deliveryman_App_Version_Control' => 'Deliveryman App Version Control',
  'Minimum_Deliveryman_App_Version' => 'Minimum Deliveryman App Version',
  'The_minimum_deliveryman_app_version_required_for_the_app_functionality.' => 'The minimum deliveryman app version required for the app functionality.',
  'Download_URL_for_Deliveryman_App' => 'Download URL for Deliveryman App',
  'Users_will_download_the_latest_deliveryman_app_version_using_this_URL.' => 'Users will download the latest deliveryman app version using this URL.',
  'What_is_App_Version?' => 'What is App Version',
  'This_app_version_defines_the_Store,_Deliveryman,_and_User_app_version_of_6amMart.' => 'This app version defines the Store  Deliveryman  and User app version of 6amMart.',
  'It_doesn’t_represent_the_Play_Store_or_App_Store_version.' => 'It doesn’t represent the Play Store or App Store version.',
  'App Download Link' => 'App Download Link',
  'The_app_download_link_is_the_URL_from_which_users_can_update_the_app_by_clicking_the_`Update_App`_button_from_their_app.' => 'The app download link is the URL from which users can update the app by clicking the `Update App` button from their app.',
  'DB_clean' => 'DB clean',
  'Clean database' => 'Clean database',
  'note_:' => 'Note :',
  'This_page_contains_sensitive_information.Make_sure_before_changing.' => 'This page contains sensitive information.Make sure before changing.',
  'table_unchecked_warning' => 'Table unchecked warning',
  'Sensitive_data! Make_sure_before_changing.' => 'Sensitive data! Make sure before changing.',
  'How_the_Setting_Works' => 'How the Setting Works',
  'get_your_zip_file_from_the_purchased_theme_and_upload_it_and_activate_theme_with_your_Codecanyon_username_and_purchase_code' => 'Get your zip file from the purchased theme and upload it and activate theme with your Codecanyon username and purchase code',
  'now_you’ll_be_successfully_able_to_use_the_theme_for_your_6Valley_website' => 'Now you’ll be successfully able to use the theme for your 6Valley website',
  'N:B you_can_upload_only_6Valley’s_theme_templates' => 'N:B you can upload only 6Valley’s theme templates',
  'upload_theme' => 'Upload theme',
  'please_make_sure' => 'Please make sure',
  'your_server_php' => 'Your server php',
  'current_value_is' => 'Current value is',
  'value_is_grater_or_equal_to_20MB' => 'Value is grater or equal to 20MB',
  'are_you_sure?' => 'Are you sure ?',
  'want_to_change_status' => 'Want to change status',
  'codecanyon_username' => 'Codecanyon username',
  'purchase_code' => 'Purchase code',
  'activate' => 'Activate',
  'Dispatch Overview' => 'Dispatch Overview',
  'Hello, here you can manage your dispatch orders.' => 'Hello  here you can manage your dispatch orders.',
  'Available to assign more order' => 'Available to assign more order',
  'Fully Booked Delivery Man' => 'Fully Booked Delivery Man',
  'dispatch_section' => 'Dispatch section',
  'dispatch_management' => 'Dispatch management',
  'ongoingOrders' => 'OngoingOrders',
  'searching_for_deliverymen' => 'Searching for deliverymen',
  'Filters' => 'Filters',
  'home_delivery' => 'Home delivery',
  'Clear_all_filters' => 'Clear all filters',
  'on_going' => 'On going',
  'updated_successfully!' => 'Updated successfully!',
  'Download_Seller_App_From_Playstore' => 'Download Seller App From Playstore',
  'Become a best' => 'Become a best',
  'Seller' => 'Seller',
  'Become a smart' => 'Become a smart',
  'Deliveryman' => 'Deliveryman',
  'Still increasing' => 'Still increasing',
  'Download_the_User_App_from_Playstore' => 'Download the User App from Playstore',
  'Download_the_User_App_from_Applestore' => 'Download the User App from Applestore',
  'contact_us' => 'Contact us',
  'Join us' => 'Join us',
  'store_registration' => 'Store registration',
  'deliveryman_registration' => 'Deliveryman registration',
  'Suppport' => 'Suppport',
  'Contact_Us' => 'Contact Us',
  'Terms_And' => 'Terms And',
  'Conditions' => 'Conditions',
  'Call_Us' => 'Call Us',
  'Email' => 'Email',
  'Address' => 'Address',
  'Time' => 'Time',
  'Send_Message' => 'Send Message',
  'application' => 'Application',
  'approx_delivery_time' => 'Approx delivery time',
  'upload_cover_photo' => 'Upload cover photo',
  'store_logo' => 'Store logo',
  'module_change_warning' => 'Module change warning',
  'login_info' => 'Login info',
  'password_not_matched' => 'Password not matched',
  'Application' => 'Application',
  'order_unavailable_item_note' => 'Order unavailable item note',
  'take away' => 'Take away',
  'partially_paid_amount' => 'Partially paid amount',
  'delivery_proof' => 'Delivery proof',
  'vendor' => 'Vendor',
  'followup' => 'Followup',
  'dashboard_order_statistics' => 'Dashboard order statistics',
  'Overall Statistics' => 'Overall Statistics',
  'Today\'s Statistics' => 'Today s Statistics',
  'This Month\'s Statistics' => 'This Month s Statistics',
  'cooking' => 'Cooking',
  'ready_for_delivery' => 'Ready for delivery',
  'item_on_the_way' => 'Item on the way',
  'commission_given' => 'Commission given',
  'yearly_statistics' => 'Yearly statistics',
  'top_selling_items' => 'Top selling items',
  'top_rated_items' => 'Top rated items',
  'order_section' => 'Order section',
  'confirmed_orders' => 'Confirmed orders',
  'items_on_the_way' => 'Items on the way',
  'item_management' => 'Item management',
  'items_list' => 'Items list',
  'stock_limit_list' => 'Stock limit list',
  'deliverymen_list' => 'Deliverymen list',
  'marketing_section' => 'Marketing section',
  'Item Campaigns' => 'Item Campaigns',
  'storeConfig' => 'Store config',
  'my_shop' => 'My shop',
  'bank_info' => 'Bank info',
  'my_wallet' => 'My wallet',
  'chat' => 'Chat',
  'Chat' => 'Chat',
  'Report_section' => 'Report section',
  'employee_section' => 'Employee section',
  'store_settings' => 'Store settings',
  'New message arrived' => 'New message arrived',
  'ex_:_search_order_id' => 'Ex : search order id',
  'column' => 'Column',
  'Delivery Man' => 'Delivery Man',
  'deliveryman_not_found' => 'Deliveryman not found',
  'order_proof_image' => 'Order proof image',
  'orders_delivered' => 'Orders delivered',
  'add_delivery_proof' => 'Add delivery proof',
  'update_order_amount' => 'Update order amount',
  'update_discount_amount' => 'Update discount amount',
  'discount_amount' => 'Discount amount',
  'Enter order verification code' => 'Enter order verification code',
  'store_temporarily_closed_title' => 'Store temporarily closed title',
  'scheduled_order' => 'Scheduled order',
  'When_enabled,_store_owner_can_take_scheduled_orders_from_customers.' => 'When enabled  store owner can take scheduled orders from customers.',
  'scheduled_order_hint' => 'Scheduled order hint',
  'When_enabled,_customers_can_make_home_delivery_orders_from_this_store.' => 'When enabled  customers can make home delivery orders from this store.',
  'home_delivery_hint' => 'Home delivery hint',
  'When_enabled,_customers_can_place_takeaway_orders_from_this_store.' => 'When enabled  customers can place takeaway orders from this store.',
  'take_away_hint' => 'Take away hint',
  'If this option is on, customers will get free delivery' => 'If this option is on  customers will get free delivery',
  'basic_settings' => 'Basic settings',
  'minimum_order_amount' => 'Minimum order amount',
  'Specify_the_minimum_order_amount_required_for_customers_when_ordering_from_this_store.' => 'Specify the minimum order amount required for customers when ordering from this store.',
  'self_delivery_hint' => 'Self delivery hint',
  'minimum_processing_time' => 'Minimum processing time',
  'minimum_processing_time_warning' => 'Minimum processing time warning',
  'Set_the_total_time_to_deliver_products.' => 'Set the total time to deliver products.',
  'minimum_delivery_time' => 'Minimum delivery time',
  'maximum_delivery_time' => 'Maximum delivery time',
  'delivery_charge_per_km' => 'Delivery charge per km',
  'maximum_delivery_charge' => 'Maximum delivery charge',
  'It will add a limite on total delivery charge.' => 'It will add a limite on total delivery charge.',
  'gst' => 'Gst',
  'gst_status_warning' => 'Gst status warning',
  'store_meta_data' => 'Store meta data',
  'meta_title' => 'Meta title',
  'meta_description' => 'Meta description',
  'store_meta_image' => 'Store meta image',
  'meta_image' => 'Meta image',
  'Daily time schedule' => 'Daily time schedule',
  'opening_time' => 'Opening time',
  'closing_time' => 'Closing time',
  'thirsday' => 'Thirsday',
  'Create Schedule For ' => 'Create Schedule For',
  'Start time' => 'Start time',
  'End time' => 'End time',
  'you_want_to_temporarily_close_this_store' => 'You want to temporarily close this store',
  'Want_to_delete_this_schedule?' => 'Want to delete this schedule',
  'If_you_select_Yes,_the_time_schedule_will_be_deleted.' => 'If you select Yes  the time schedule will be deleted.',
  'Schedule removed successfully' => 'Schedule removed successfully',
  'Schedule not found' => 'Schedule not found',
  'Schedule added successfully' => 'Schedule added successfully',
  'order_proof_added' => 'Order proof added',
  'ex_search_name' => 'Ex search name',
  'Name' => 'Name',
  'Recommended' => 'Recommended',
  'Recommend_to_customers' => 'Recommend to customers',
  'Update Item' => 'Update Item',
  'item_info' => 'Item info',
  'item_images' => 'Item images',
  'order_proof_image_removed_successfully' => 'Order proof image removed successfully',
  'all_image_delete_warning' => 'You cannot delete all images!',
  'order_proof_must_not_have_more_than_5_item' => 'Order proof must not have more than 5 item',
  'language_content_table' => 'Language content table',
  'Ex : Search' => 'Ex : Search',
  'SL#' => 'SL#',
  'Current_value' => 'Current value',
  'translated_value' => 'Translated value',
  'auto_translate' => 'Auto translate',
  'text_updated_successfully' => 'Text updated successfully',
  'Key removed successfully' => 'Key removed successfully',
  'Key translated successfully' => 'Key translated successfully',
  'overview' => 'Overview',
  'discounts' => 'Discounts',
  'Conversations' => 'Conversations',
  'meta_data' => 'Meta data',
  'collected_cash_by_store' => 'Collected cash by store',
  'collect_cash_from_store' => 'Collect cash from store',
  'pending_withdraw' => 'Pending withdraw',
  'total_withdrawal_amount' => 'Total withdrawal amount',
  'withdraw_able_balance' => 'Withdraw able balance',
  'No Data found' => 'No Data found',
  'Review_list' => 'Review list',
  'You_have_no_business_landing_page_to_show._If_user_search_landing_page_URL_they_will_see_404_page.' => 'You have no business landing page to show. If user search landing page URL they will see 404 page.',
  'Want_to_Turn_On_the_Default_Admin_Landing_Page_?' => 'Want to Turn On the Default Admin Landing Page ?',
  'If_enabled,_the_landing_page_will_be_visible_to_everyone' => 'If enabled  the landing page will be visible to everyone',
  'landing_page_is_on.' => 'Landing page is on.',
  'about_us_updated' => 'About us updated',
  'delivery_man_list' => 'Delivery man list',
  'Search_Criteria' => 'Search Criteria',
  'Search_Bar_Content' => 'Search Bar Content',
  'N/A' => 'N/A',
  'Analytics' => 'Analytics',
  'inactive_delivery_man' => 'Inactive delivery man',
  'delivery_man_type' => 'Delivery man type',
  'total_completed' => 'Total completed',
  'total_running_orders' => 'Total running orders',
  'customer_list' => 'Customer list',
  'Customer_Analytics' => 'Customer Analytics',
  'Total_Customer' => 'Total Customer',
  'Active_Customer' => 'Active Customer',
  'Inactive_Customer' => 'Inactive Customer',
  'Search_Bar_Conten' => 'Search Bar Conten',
  'saved_address' => 'Saved address',
  'total_wallet_amount' => 'Total wallet amount',
  'total_loyality_points' => 'Total loyality points',
  'Active' => 'Active',
  'are_you_sure_you_want_to_delete_the_theme' => 'Are you sure you want to delete the theme ?',
  'once_you_delete' => 'Once you delete',
  'you_will_lost_the_this_theme' => 'You will lost the this theme',
  'file_delete_successfully' => 'File delete successfully',
  'file_upload_fail!' => 'File upload fail!',
  'file_upload_successfully!' => 'File upload successfully!',
  'Codecanyon_usename' => 'Codecanyon usename',
  'Ex:_Riad_Uddin' => 'Ex: Riad Uddin',
  'Purchase_Code' => 'Purchase Code',
  'Ex: 987652' => 'Ex: 987652',
  'Activate' => 'Activate',
  'activated_successfully' => 'Activated successfully',
  'Payment Setup' => 'Payment Setup',
  'payment_gateway_configuration' => 'Payment gateway configuration',
  'payment_gateway_title' => 'Payment gateway title',
  'Your_current_payment_settings_are_disabled,_because_you_have_enabled_payment_gateway_addon,_To_visit_your_currently_active_payment_gateway_settings_please_follow_the_link.' => 'Your current payment settings are disabled  because you have enabled payment gateway addon  To visit your currently active payment gateway settings please follow the link.',
  'Live' => 'Live',
  'Test' => 'Test',
  'Deducting the admin commission on the delivery fee, the delivery fee & tips amount goes to earning section.' => 'Deducting the admin commission on the delivery fee  the delivery fee & tips amount goes to earning section.',
  'payment_reference_code_is_added' => 'Payment reference code is added',
  'firebase' => 'Firebase',
  'manual' => 'Manual',
  'Order_Notification_Type' => 'Order Notification Type',
  'successfully_updated_to_changes_restart_app' => 'Successfully updated to changes restart app',
  'updated_successfully' => 'Updated successfully',
  'order_push_title' => 'Order push title',
  'new_order_push_description' => 'New order push description',
  'order_placed_successfully' => 'Order placed successfully',
  'Cancelled_By' => 'Cancelled By',
  'order_setup' => 'Order setup',
  'Change status to pending ?' => 'Change status to pending ?',
  'Change status to confirmed ?' => 'Change status to confirmed ?',
  'Change status to processing ?' => 'Change status to processing ?',
  'Change status to handover ?' => 'Change status to handover ?',
  'handover' => 'Handover',
  'Change status to out for delivery ?' => 'Change status to out for delivery ?',
  'Change status to delivered (payment status will be paid if not)?' => 'Change status to delivered (payment status will be paid if not)',
  'last_location' => 'Last location',
  'add_Offline_Payment_Method' => 'Add Offline Payment Method',
  'payment_Information' => 'Payment Information',
  'Add_New_Field' => 'Add New Field',
  'payment_Method_Name' => 'Payment Method Name',
  'ex' => 'Ex',
  'input_field_Name' => 'Input field Name',
  'bank_Name' => 'Bank Name',
  'input_Data' => 'Input Data',
  'AVC' => 'AVC',
  'bank' => 'Bank',
  'required_Information_from_Customer' => 'Required Information from Customer',
  'payment_By' => 'Payment By',
  'place_Holder' => 'Place Holder',
  'enter_name' => 'Enter name',
  'is_Required' => 'Is Required',
  'Offline_Payment_Setup' => 'Offline Payment Setup',
  'offline_payment_method_added_successfully' => 'Offline payment method added successfully',
  'offline_Payment_Method' => 'Offline Payment Method',
  'add_New_Method' => 'Add New Method',
  'payment_Info' => 'Payment Info',
  'required_Info_From_Customer' => 'Required Info From Customer',
  'offline_payment_method_delete_successfully' => 'Offline payment method delete successfully',
  'Offline_Payment_Method_Setup' => 'Offline Payment Method Setup',
  'edit_Offline_Payment_Method' => 'Edit Offline Payment Method',
  'AVC_bank' => 'AVC bank',
  'Want_to_delete_this_offline_payment_method' => 'Want to delete this offline payment method',
  'status_update_failed' => 'Status update failed',
  'Status update failed' => 'Status update failed',
  'Status updated successfully' => 'Status updated successfully',
  'Want_to_enable_this_offline_payment_method?' => 'Want to enable this offline payment method',
  'Want_to_disable_this_offline_payment_method?' => 'Want to disable this offline payment method',
  'It_will_be_available_on_the_user_views.' => 'It will be available on the user views.',
  'It_will_be_hidden_from_the_user_views.' => 'It will be hidden from the user views.',
  'offline_payment_method_update_successfully' => 'Offline payment method update successfully',
  'Payment_Note' => 'Payment Note',
  'Offline_Payment' => 'Offline Payment',
  'By Turning ON Offline_Payment Option' => 'By Turning ON Offline Payment Option',
  'By Turning OFF Offline_Payment Option' => 'By Turning OFF Offline Payment Option',
  'Customers will not be able to select Offline_Payment as a payment method during checkout. Please review your settings and enable Offline_Payment if you wish to offer this payment option to customers.' => 'Customers will not be able to select Offline Payment as a payment method during checkout. Please review your settings and enable Offline Payment if you wish to offer this payment option to customers.',
  'Customers will be able to select Offline_Payment as a payment method during checkout.' => 'Customers will be able to select Offline Payment as a payment method during checkout.',
  'scheduled_at' => 'Scheduled at',
  'assign_delivery_mam_manually' => 'Assign delivery mam manually',
  'offline_payment_for_the_order_not_available_at_this_time' => 'Offline payment for the order not available at this time',
  'method_name' => 'Method name',
  'your_name' => 'Your name',
  't_id' => 'T id',
  'due_amount' => 'Due amount',
  'Want to delete this index_page ?' => 'Want to delete this index page ?',
  'delete_index_page' => 'Delete index page',
  'File_deleted_successfully' => 'File deleted successfully',
  'landing_page_is_off.' => 'Landing page is off.',
  'invalid_file!' => 'Invalid file!',
  'zip_file_is_required' => 'Zip file is required',
  'add_your_paymen_ref_first' => 'Add your paymen ref first',
  'order_status_updated' => 'Order status updated',
  'For_Firebase,_a_single_real-time_notification_will_be_sent_upon_order_placement,_with_no_repetition._For_the_Manual_option,_notifications_will_appear_at_10-second_intervals_until_the_order_is_viewed.' => 'For Firebase  a single real-time notification will be sent upon order placement  with no repetition. For the Manual option  notifications will appear at 10-second intervals until the order is viewed.',
  'On_every_purchase_this_percent_of_amount_will_be_added_as_loyalty_point_on_his_account' => 'On every purchase this percent of amount will be added as loyalty point on his account',
  'item_default_name_required' => 'Item default name required',
  'item_default_description_required' => 'Item default description required',
  'category_required' => 'Category required',
  'description_length_warning' => 'Description length warning',
  'your_product_added_for_approval' => 'Your product added for approval',
  'you_want_to_approve_this_product' => 'You want to approve this product',
  'you_want_to_deny_this_product' => 'You want to deny this product',
  'Enter_a_reason' => 'Enter a reason',
  'Approval_list' => 'Approval list',
  'Product_approved' => 'Product approved',
  'Thanks & Regards' => 'Thanks & Regards',
  'Email_Template' => 'Email Template',
  'Product_denied' => 'Product denied',
  'rejected' => 'Rejected',
  'Pending_item_list' => 'Pending item list',
  'pending_item_list' => 'Pending item list',
  'available_time_schedule' => 'Available time schedule',
  'Rejection_Note' => 'Rejection Note',
  'default_name_is_required' => 'Default name is required',
  'default_description_is_required' => 'Default description is required',
  'item_image_removed_successfully' => 'Item image removed successfully',
  'TAX_Included' => 'TAX Included',
  'quantity' => 'Quantity',
  'Total Price' => 'Total Price',
  'add_to_cart' => 'Add to cart',
  'Product_Rejection' => 'Product Rejection',
  'Send_Mail_on_Product_Deny’?' => 'Send Mail on Product Deny’',
  'If_Admin_rejects_a_Store’s_product,_the_store_will_get_an_automatic_disapproval_mail_from_the_system.' => 'If Admin rejects a Store’s product  the store will get an automatic disapproval mail from the system.',
  'Want_to_enable_Store_product_deny_mail?' => 'Want to enable Store product deny mail',
  'Want_to_disable_Store_product_deny_mail?' => 'Want to disable Store product deny mail',
  'If_enabled,_Users_will_receive_a_confirmation_email_when_the_Admin_rejects_their_product.' => 'If enabled  Users will receive a confirmation email when the Admin rejects their product.',
  'If_disabled,__Users_will_not_get_a_product_rejection_mail.' => 'If disabled   Users will not get a product rejection mail.',
  'Send_Mail_on_Product_Approval’?' => 'Send Mail on Product Approval’',
  'If_Admin_approves_a_Store’s_product,_the_store_will_get_an_automatic_disapproval_mail_from_the_system.' => 'If Admin approves a Store’s product  the store will get an automatic disapproval mail from the system.',
  'Want_to_enable_product_approval_mail?' => 'Want to enable product approval mail',
  'Want_to_disable__product_approval_mai?' => 'Want to disable  product approval mai',
  'If_enabled,_Store_will_receive_a_confirmation_email_when_the_Admin_approves_their_product.' => 'If enabled  Store will receive a confirmation email when the Admin approves their product.',
  'If_disabled,__Users_will_not_get_a_product_approval_mail.' => 'If disabled   Users will not get a product approval mail.',
  'product_approval' => 'Product approval',
  'If_enabled,_this_option_to_require_admin_approval_for_products_to_be_displayed_on_the_user_side.' => 'If enabled  this option to require admin approval for products to be displayed on the user side.',
  'Want_to_enable_product_approval?' => 'Want to enable product approval',
  'Want_to_disable_product_approval?' => 'Want to disable product approval',
  'If_you_enable_this,_option_to_require_admin_approval_for_products_to_be_displayed_on_the_user_side' => 'If you enable this  option to require admin approval for products to be displayed on the user side',
  'If_you_disable_this,products_will_to_be_displayed_on_the_user_side_without_admin_approval.' => 'If you disable this products will to be displayed on the user side without admin approval.',
  '(size: 2:1)' => '(size: 2:1)',
  'If_you_want_to_upload_one_banner_then_you_have_to_upload_it_in_2:1_ratio_otherwise_the_ratio_will_be_same_as_before.' => 'If you want to upload one banner then you have to upload it in 2:1 ratio otherwise the ratio will be same as before.',
  'Image_removed_successfully' => 'Image removed successfully',
  'Product_Gallery' => 'Product Gallery',
  'If_enabled,can_create_duplicate_products.' => 'If enabled can create duplicate products.',
  'Want_to_enable_product_gallery?' => 'Want to enable product gallery',
  'Want_to_disable_product_gallery?' => 'Want to disable product gallery',
  'If_you_enable_this,can_create_duplicate_products' => 'If you enable this can create duplicate products',
  'If_you_disable_this,can_not_create_duplicate_products.' => 'If you disable this can not create duplicate products.',
  'Add item' => 'Add item',
  'item_name_required' => 'Item name required',
  'Image_is_required' => 'Image is required',
  'Add_item' => 'Add item',
  'Banner_Setup' => 'Banner Setup',
  'Redirection_URL_/_Link' => 'Redirection URL / Link',
  'Enter_URL' => 'Enter URL',
  'Upload_Banner' => 'Upload Banner',
  'banner_Image' => 'Banner Image',
  'redirection_Link' => 'Redirection Link',
  'banner_deleted_successfully' => 'Banner deleted successfully',
  'store_view' => 'Store view',
  'my_store_info' => 'My store info',
  'edit_store_information' => 'Edit store information',
  'Announcement' => 'Announcement',
  'publish' => 'Publish',
  'ex_:_ABC_Company' => 'Ex : ABC Company',
  'Banner_Image_Ratio_3:1' => 'Banner Image Ratio 3:1',
  'image_format_:_jpg_,_png_,_jpeg_|_maximum_size:_2_MB' => 'Image format : jpg, png, jpeg | Maximum Size: 2 MB',
  'Offday' => 'Offday',
  'Customer_will_see_there_banners_in_your_store_details_page_in_website_and_user_apps.' => 'Customer will see there banners in your store details page in website and user apps.',
  'Update_Banner' => 'Update Banner',
  'customer_settings_updated_successfully' => 'Customer settings updated successfully',
  'access_all_products' => 'Access all products',
  'Want_to_enable_access_all_products?' => 'Want to enable access all products',
  'Want_to_disable_access_all_products?' => 'Want to disable access all products',
  'If_you_enable_this,_' => 'If you enable this',
  'If_you_enable_this,_Stores_can_access_all_products_of_other_available_stores' => 'If you enable this  Stores can access all products of other available stores',
  'If_you_disable_this,_Stores_can_not_access_all_products_of_other_stores.' => 'If you disable this  Stores can not access all products of other stores.',
  'main' => 'Main',
  'Food' => 'Food',
  'product_deleted_successfully' => 'Product deleted successfully',
  'reviewer_table_list' => 'Reviewer table list',
  'discount_can_not_be_more_than_or_equal' => 'Discount can not be more than or equal',
  'search_data' => 'Search data',
  'Add_From_Product_Gallery' => 'Add From Product Gallery',
  'all_category' => 'All category',
  'all_sub_category' => 'All sub category',
  'New_Product_Request' => 'New Product Request',
  'Item_List' => 'Item List',
  'Filter_Criteria' => 'Filter Criteria',
  'Module' => 'Module',
  'Item_Name' => 'Item Name',
  'Description' => 'Description',
  'Category_Name' => 'Category Name',
  'Sub_Category_Name' => 'Sub Category Name',
  'Available_Stock' => 'Available Stock',
  'Available_Variations' => 'Available Variations',
  'Item_Unit' => 'Item Unit',
  'Discount' => 'Discount',
  'Discount_Type' => 'Discount Type',
  'Available_From' => 'Available From',
  'Available_Till' => 'Available Till',
  'Tags' => 'Tags',
  'Store_Name' => 'Store Name',
  'New_Item_Request' => 'New Item Request',
  'New_Item_requests' => 'New Item requests',
  'All' => 'All',
  'View' => 'View',
  'multiple_select' => 'Multiple select',
  'required' => 'Required',
  'Min_select' => 'Min select',
  'Max_select' => 'Max select',
  'Reject' => 'Reject',
  'Update_item' => 'Update item',
  'General_information' => 'General information',
  'Category' => 'Category',
  'Sub_Category' => 'Sub Category',
  'Item_type' => 'Item type',
  'Unit' => 'Unit',
  'General_Information' => 'General Information',
  'price_Information' => 'Price Information',
  'Unit_Price' => 'Unit Price',
  'included' => 'Included',
  'single_select' => 'Single select',
  'Need_Approval_for_Products' => 'Need Approval for Products',
  'Add_new_product' => 'Add new product',
  'Update_product_price' => 'Update product price',
  'Update_product_variation' => 'Update product variation',
  'Update_anything_in_product_details' => 'Update anything in product details',
  'Need_Approval_When' => 'Need Approval When',
  'collect cash' => 'Collect cash',
  'customerList' => 'CustomerList',
  'All Items' => 'All Items',
  'Active Items' => 'Active Items',
  'Inactive Items' => 'Inactive Items',
  'Search by name...' => 'Search by name...',
  'discount_info' => 'Discount info',
  '* When this discount is available, is applied on all the items in this stores.' => '* When this discount is available  is applied on all the items in this stores.',
  'add_discount' => 'Add discount',
  'no_discount_created_yet' => 'No discount created yet',
  'cash_transaction' => 'Cash transaction',
  'withdraw_transactions' => 'Withdraw transactions',
  'balance_before_transaction' => 'Balance before transaction',
  'manage_item_setup' => 'Manage item setup',
  'When_disabled,_item_management_feature_will_be_hidden_from_store_panel_&_store_app' => 'When disabled  item management feature will be hidden from store panel & store app',
  'Show_Reviews_In_Store_Panel' => 'Show Reviews In Store Panel',
  'When_enabled,_store_owners_can_see_customer_feedback_in_the_store_panel_&_store_app.' => 'When enabled  store owners can see customer feedback in the store panel & store app.',
  'include_POS_in_store_panel' => 'Include POS in store panel',
  'Enable_or_Disable_Point_of_Sale_(POS)_in_the_store_panel.' => 'Enable or Disable Point of Sale (POS) in the store panel.',
  'pos_system_hint' => 'Pos system hint',
  'Store-managed_Delivery' => 'Store-managed Delivery',
  'When_this_option_is_enabled,_stores_must_deliver_orders_using_their_own_deliverymen._Plus,_stores_will_get_the_option_to_add_their_own_deliverymen_from_the_store_panel.' => 'When this option is enabled  stores must deliver orders using their own deliverymen. Plus  stores will get the option to add their own deliverymen from the store panel.',
  'takeaway' => 'Takeaway',
  'Set_the_total_time_to_process_the_order_after_order_confirmation.' => 'Set the total time to process the order after order confirmation.',
  'When_enabled,_admin_will_only_receive_the_certain_commission_percentage_he_set_for_this_store._Otherwise,_the_system_default_commission_will_be_applied.' => 'When enabled  admin will only receive the certain commission percentage he set for this store. Otherwise  the system default commission will be applied.',
  'Create Schedule' => 'Create Schedule',
  'If_you_select_Yes,_the_time_schedule_will_be_deleted' => 'If you select Yes  the time schedule will be deleted',
  'ex_:_order_id' => 'Ex : order id',
  'All_Items' => 'All Items',
  'Active_Items' => 'Active Items',
  'Inactive_Items' => 'Inactive Items',
  'Pending_for_Approval' => 'Pending for Approval',
  'Rejected_Items' => 'Rejected Items',
  'item_status_updated' => 'Item status updated',
  'Food_List' => 'Food List',
  'Zone' => 'Zone',
  'Total_items' => 'Total items',
  'Inactive_items' => 'Inactive items',
  'Food_Type' => 'Food Type',
  'Available_Addons' => 'Available Addons',
  'Non_Veg' => 'Non Veg',
  'Inactive' => 'Inactive',
  'Pending' => 'Pending',
  'Veg' => 'Veg',
  'Product_Details' => 'Product Details',
  'Your_Item_Has_Been_Rejected' => 'Your Item Has Been Rejected',
  'Pending_For_Approval_products' => 'Pending For Approval products',
  'This_Item_Is_Under_Review' => 'This Item Is Under Review',
  'edit_&_Resubmit' => 'Edit & Resubmit',
  'use_this_product_info' => 'Use this product info',
  'Is_Organic' => 'Is Organic',
  'search_product_and_use_its_info_to_create_own_product' => 'Search product and use its info to create own product',
  'Product_List' => 'Product List',
  'search_product_and_use_its_info_to_create_new_product' => 'Search product and use its info to create new product',
  'add_new_condition' => 'Add new condition',
  'Common_Condition_Setup' => 'Common Condition Setup',
  'Common_Conditions' => 'Common Conditions',
  'Common_Condition_Name' => 'Common Condition Name',
  'Total_Products' => 'Total Products',
  'new_condition' => 'New condition',
  'Name is required!' => 'Name is required!',
  'common_condition_added_successfully' => 'Common condition added successfully',
  'common_condition_status_updated' => 'Common condition status updated',
  'edit_condition' => 'Edit condition',
  'Want to delete this condition' => 'Want to delete this condition',
  'delete_condition' => 'Delete condition',
  'Update condition' => 'Update condition',
  'Common_Condition_Update' => 'Common Condition Update',
  'Failed Orders' => 'Failed Orders',
  'dispatch' => 'Dispatch',
  'category_setup' => 'Category setup',
  'delivery_section' => 'Delivery section',
  'delivery_management' => 'Delivery management',
  'delivery_fee_setup' => 'Delivery fee setup',
  'Suitable_For' => 'Suitable For',
  'Select_Condition' => 'Select Condition',
  'Is_Basic_Medicine' => 'Is Basic Medicine',
  'update_store' => 'Update store',
  'Confirm Password' => 'Confirm Password',
  'is_basic' => 'Is basic',
  ' +data[count].name +  ' => ' +data[count].name +',
  'For ' => 'For',
  '$mod- module_name' => '$mod- module name',
  '( Ratio 190x120 )' => '( Ratio 190x120 )',
  '* This discount is applied on all the foods in your restaurant' => '* This discount is applied on all the foods in your restaurant',
  '* This discount is applied on all the items in your store' => '* This discount is applied on all the items in your store',
  '* When this discount is available  is applied on all the items in this stores.' => '* When this discount is available  is applied on all the items in this stores.',
  '*By Turning OFF mail configuration  all your mailing services will be off.' => '*By Turning OFF mail configuration  all your mailing services will be off.',
  '*By Turning ON Refund Mode  Customers Can Sent Refund Requests' => '*By Turning ON Refund Mode  Customers Can Sent Refund Requests',
  '*Customers_cannot_request_a_Refund_if_the_Admin_does_not_specify_a_cause_for_cancellation _even_though_they_see_the_Cancel_Order_option._So_Admin_MUST_provide_a_proper_Refund_Reason_and_select_the_related_user.' => '*Customers cannot request a Refund if the Admin does not specify a cause for cancellation  even though they see the Cancel Order option. So Admin MUST provide a proper Refund Reason and select the related user.',
  '*If_the_Admin_enables_the_âRefund_Request_Modeâ _customers_can_request_a_refund.' => '*If the Admin enables the âRefund Request Modeâ  customers can request a refund.',
  '*If_the_Admin_enables_the_âRefund_Request_Modeâ _customers_can_request_a_refund.' => '*If the Admin enables the âRefund Request Modeâ  customers can request a refund.',
  '*If_the_Admin_enables_the_‘Refund_Request_Mode’ _customers_can_request_a_refund.' => '*If the Admin enables the ‘Refund Request Mode’  customers can request a refund.',
  'Other_Promotional_Content_Setup' => 'Other Promotional Content Setup',
  'Bottom_Section_Banner' => 'Bottom Section Banner',
  'Best_Reviewed_Section_Banner' => 'Best Reviewed Section Banner',
  'Banner_Image_Ratio_4:1' => 'Banner Image Ratio 4:1',
  'Min_Size_for_Better_Resolution_235_x_375_px' => 'Min Size for Better Resolution 235 x 375 px',
  'this_banner_is_only_for_react_web.' => 'This banner is only for react web.',
  'Banner_Image_Ratio_5:1' => 'Banner Image Ratio 5:1',
  'Basic_Medicine_Nearby' => 'Basic Medicine Nearby',
  'Best_Reviewed_Items' => 'Best Reviewed Items',
  'New Arrivals' => 'New Arrivals',
  'other_banners' => 'Other banners',
  'banner_setup_updated' => 'Banner setup updated',
  'promotional_banners' => 'Promotional banners',
  'banner_status_updated' => 'Banner status updated',
  'Promotional_Banner_Edit' => 'Promotional Banner Edit',
  'banner_updated' => 'Banner updated',
  'image (1:1)' => 'Image (1:1)',
  'Write_the_short_description_within_60_characters' => 'Write the short description within 60 characters',
  'short_description_here...' => 'Short description here...',
  'criteria_added_successfully' => 'Criteria added successfully',
  'Write_the_short_description_within_100_characters' => 'Write the short description within 100 characters',
  'This section will be enabled. You can see this section on this module page.' => 'This section will be enabled. You can see this section on this module page.',
  'If_yes,_it_will_be_available_on_this_module.' => 'If yes  it will be available on this module.',
  'If_yes,_it_will_be_hidden_from_this_module.' => 'If yes  it will be hidden from this module.',
  'If_yes,_It_will_be_removed_from_this_list_and_this_module.' => 'If yes  It will be removed from this list and this module.',
  'video' => 'Video',
  'content-1' => 'Content-1',
  'Video_/_Image_Content' => 'Video / Image Content',
  'Image_Size_Min_615_x_350_px' => 'Image Size Min 615 x 350 px',
  'Video_Link' => 'Video Link',
  'Video_/_Image' => 'Video / Image',
  'content-2' => 'Content-2',
  'content-3' => 'Content-3',
  'video_/_image_content_setup_updated' => 'Video / image content setup updated',
  'Upload_Content' => 'Upload Content',
  'module_id_required' => 'Module id required',
  'flash_sales' => 'Flash sales',
  'flash_sale_setup' => 'Flash sale setup',
  'ex_:_new_flash_sale' => 'Ex : new flash sale',
  'flash_sale_list' => 'Flash sale list',
  'ex_:_flash_sale_name' => 'Ex : flash sale name',
  'title is required!' => 'Title is required!',
  'default_data_is_required' => 'Default data is required',
  'flash_sale_added_successfully' => 'Flash sale added successfully',
  'Want to delete this flash_sale ?' => 'Want to delete this flash sale  ?',
  'duration' => 'Duration',
  'active_products' => 'Active products',
  'flash_sale_publish_updated' => 'Flash sale publish updated',
  'ex_:_flash_sale_title' => 'Ex : flash sale title',
  'Update Flash Sale' => 'Update Flash Sale',
  'flash_sale_update' => 'Flash sale update',
  'updated_flash_sale' => 'Updated flash sale',
  'flash_sale_updated_successfully' => 'Flash sale updated successfully',
  'add-product' => 'Add-product',
  'Add new campaign' => 'Add new campaign',
  'Select Store' => 'Select Store',
  'Campaign uploaded successfully' => 'Campaign uploaded successfully',
  'product' => 'Product',
  'select_product' => 'Select product',
  'current_active_discount' => 'Current active discount',
  'product is required!' => 'Product is required!',
  'Item_added_successfully' => 'Item added successfully',
  'stock_for_this_sale' => 'Stock for this sale',
  'Item_stock_exceeded' => 'Item stock exceeded',
  'Item_already_exists' => 'Item already exists',
  'item_deleted_successfully' => 'Item deleted successfully',
  'flash_sale_product_setup' => 'Flash sale product setup',
  'flash_sale_product_list' => 'Flash sale product list',
  'ex_:_product_name' => 'Ex : product name',
  'conversation' => 'Conversation',
  'Search' => 'Search',
  'store_type' => 'Store type',
  'Define_the_food_type_this_store_can_sell.' => 'Define the food type this store can sell.',
  'settings_updated' => 'Settings updated',
  'now_you’ll_be_successfully_able_to_use_the_theme_for_your_website' => 'Now you’ll be successfully able to use the theme for your website',
  'N:B you_can_upload_only_theme_templates' => 'N:B you can upload only theme templates',
  'want_to_disabled_this_ Payment & Sms gateways' => 'Want to disabled this  Payment & Sms gateways',
  'Addon Menus' => 'Addon Menus',
  'are_you_sure_you_want_to_delete_the' => 'Are you sure you want to delete the',
  'you_will_lost_the_this' => 'You will lost the this',
  'want_to_activate_this_ Payment & Sms gateways' => 'Want to activate this  Payment & Sms gateways',
  'Your_current_sms_settings_are_disabled,_because_you_have_enabled_sms_gateway_addon,_To_visit_your_currently_active_sms_gateway_settings_please_follow_the_link.' => 'Your current sms settings are disabled  because you have enabled sms gateway addon  To visit your currently active sms gateway settings please follow the link.',
  'twilio' => 'Twilio',
  'messaging_service_sid' => 'Messaging service sid',
  '2factor' => '2factor',
  'msg91' => 'Msg91',
  'auth_key' => 'Auth key',
  'nexmo' => 'Nexmo',
  'sms_gateways_configuration' => 'Sms gateways configuration',
  'Make_the_payment_verified_for_this_order' => 'Make the payment verified for this order',
  'Verify_Payment' => 'Verify Payment',
  'Make_the_payment_deny_for_this_order' => 'Make the payment deny for this order',
  'Deny_Payment' => 'Deny Payment',
  'Payment_verification' => 'Payment verification',
  'verified' => 'Verified',
  'Payment_status_updated' => 'Payment status updated',
  'Offline_Payment_Approve' => 'Offline Payment Approve',
  'Offline_Payment_Deny' => 'Offline Payment Deny',
  'Send_Mail_on_Offline_Payment_Deny’?' => 'Send Mail on Offline Payment Deny’ ',
  'If_Admin_rejects_an_offline_payment,_the_customer_will_get_an_automatic_disapproval_mail_from_the_system.' => 'If Admin rejects an offline payment  the customer will get an automatic disapproval mail from the system.',
  'Want_to_enable_Offline_payment_deny_mail?' => 'Want to enable Offline payment deny mail ',
  'Want_to_disable_Offline_payment_deny_mail?' => 'Want to disable Offline payment deny mail ',
  'If_enabled,_Users_will_receive_a_confirmation_email_when_the_Admin_rejects_their_Offline_payment.' => 'If enabled  Users will receive a confirmation email when the Admin rejects their Offline payment.',
  'If_disabled,__Users_will_not_get_a_Offline_payment_rejection_mail.' => 'If disabled   Users will not get a Offline payment rejection mail.',
  'Send_Mail_on_Offline_Payment’?' => 'Send Mail on Offline Payment’ ',
  'If_Admin_approves_an_offline_payment,_the_user_will_get_an_automatic_disapproval_mail_from_the_system.' => 'If Admin approves an offline payment  the user will get an automatic disapproval mail from the system.',
  'Want_to_enable_offline_payment_mail?' => 'Want to enable offline payment mail ',
  'Want_to_disable_offline_payment_mai?' => 'Want to disable offline payment mai ',
  'If_enabled,_User_will_receive_a_confirmation_email_when_the_Admin_approves_their_offline_payment.' => 'If enabled  User will receive a confirmation email when the Admin approves their offline payment.',
  'If_disabled,__Users_will_not_get_offline_payment_mail.' => 'If disabled   Users will not get offline payment mail.',
  'email_status_updated' => 'Email status updated',
  'template_added_successfully' => 'Template added successfully',
  'Want_to_enable_offline_payment_approve_mail?' => 'Want to enable offline payment approve mail ',
  'Want_to_disable_offline_payment_approve_mail?' => 'Want to disable offline payment approve mail ',
  'If_disabled,_Users_will_not_get_offline_payment_approve_mail.' => 'If disabled  Users will not get offline payment approve mail.',
  'Add_Offline_Payment_Rejection_Note' => 'Add Offline Payment Rejection Note',
  'transaction_id_mismatched' => 'Transaction id mismatched',
  'Confirm_Rejection' => 'Confirm Rejection',
  'order refund rejection' => 'Order refund rejection',
  'Payment_approved' => 'Payment approved',
  'Payment_denied' => 'Payment denied',
  'Your_Offline_payment_was_rejected' => 'Your Offline payment was rejected',
  'Offline_payment_rejection_note' => 'Offline payment rejection note',
  'Section_View' => 'Section View',
  'payment_information' => 'Payment information',
  'Add_new_field' => 'Add new field',
  'payment_method_name' => 'Payment method name',
  'Admin needs to set the required customer information, which needs to be provided to the customers before placing a booking through offline payment' => 'Admin needs to set the required customer information  which needs to be provided to the customers before placing a booking through offline payment',
  'Required Information from Customer' => 'Required Information from Customer',
  'payment_note' => 'Payment note',
  'Offline Payment' => 'Offline Payment',
  'This view is from the user app.' => 'This view is from the user app.',
  'This is how customer will see in the app' => 'This is how customer will see in the app',
  'Pay on this account' => 'Pay on this account',
  'Amount' => 'Amount',
  'Payment Info' => 'Payment Info',
  'input_field_name' => 'Input field name',
  'placeholder' => 'Placeholder',
  'This_field_required' => 'This field required',
  'Reached maximum' => 'Reached maximum',
  'data' => 'Data',
  'ex:_bkash' => 'Ex: bkash',
  'Payment_information_details_requtied' => 'Payment information details requtied',
  'Customer_input_information_requtied' => 'Customer input information requtied',
  'Payment_Verificastion' => 'Payment Verificastion',
  'Please Check & Verufy the payment information weather it is correct or not before confirm the order.' => 'Please Check & Verufy the payment information weather it is correct or not before confirm the order.',
  'Please_Check_&_Verufy_the_payment_information_weather_it_is_correct_or_not_before_confirm_the_order.' => 'Please Check & Verufy the payment information weather it is correct or not before confirm the order.',
  'Payment_Information' => 'Payment Information',
  'PaAmount' => 'PaAmount',
  'Transaction ID' => 'Transaction ID',
  'Payment By' => 'Payment By',
  'Reference' => 'Reference',
  'Offline_Payments' => 'Offline Payments',
  'Verify_Offline_Payments' => 'Verify Offline Payments',
  'For_offline_payments_please_verify_if_the_payments_are_safely_received_to_your_account._Customer_id_not_liable_if_you_confirm_and_deliver_the_orders_without_checking_payments_transactions' => 'For offline payments please verify if the payments are safely received to your account. Customer id not liable if you confirm and deliver the orders without checking payments transactions',
  'Payment_Method' => 'Payment Method',
  'Phone' => 'Phone',
  'Payment_Verification' => 'Payment Verification',
  'Payment_Didn’t_Recerive' => 'Payment Didn’t Recerive',
  'Yes,_Payment_Received' => 'Yes  Payment Received',
  'Denied' => 'Denied',
  'Recheck_Verification' => 'Recheck Verification',
  'Customer_Note' => 'Customer Note',
  'Switched_to_COD' => 'Switched to COD',
  'order_delivery_instruction' => 'Order delivery instruction',
  'confirm_this_order' => 'Confirm this order',
  'Cancel Order' => 'Cancel Order',
  'Change status to cooking ?' => 'Change status to cooking  ',
  'proceed_for_processing' => 'Proceed for processing',
  'make_ready_for_handover' => 'Make ready for handover',
  'make_delivered' => 'Make delivered',
  'Please_Verify_the_payment_before_confirm_order.' => 'Please Verify the payment before confirm order.',
  'Please_Check_&_Verify_the_payment_information_weather_it_is_correct_or_not_before_confirm_the_order.' => 'Please Check & Verify the payment information weather it is correct or not before confirm the order.',
  'Payment_Details' => 'Payment Details',
  'Enter Name' => 'Enter Name',
  'is_required_?' => 'Is required ?',
  'Bank_Name' => 'Bank Name',
  'Data' => 'Data',
  'ABC_bank' => 'ABC bank',
  'Ex:ABC_Company' => 'Ex:ABC Company',
  'guest_varified' => 'Guest varified',
  'Unauthorized' => 'Unauthorized',
  'brrm' => 'Brrm',
  'module_updated_successfully' => 'Module updated successfully',
  'Customer_Registration' => 'Customer Registration',
  'interest_updated_successfully' => 'Interest updated successfully',
  'You have to set category wise charge from parcel category' => 'You have to set category wise charge from parcel category',
  'Edit_&_Approve' => 'Edit & Approve',
  'vendor_type_required' => 'Vendor type required',
  'Recommended_Store' => 'Recommended Store',
  'Recommended_stores' => 'Recommended stores',
  'search_by_Product_or_Customer' => 'Search by Product or Customer',
  'select_Product' => 'Select Product',
  'search menu' => 'Search menu',
  'Order_Placed' => 'Order Placed',
  'item_price' => 'Item price',
  'you_need_to_order_at_least' => 'You need to order at least ',
  'Cancel_Reason' => 'Cancel Reason',
  'Cancel_Note' => 'Cancel Note',
  'Canceled_By' => 'Canceled By',
  'Amount_paid_by' => 'Amount paid by',
  'Amount_Returned_To_Wallet' => 'Amount Returned To Wallet',
  'Paid_by' => 'Paid by',
  'Due_Amount' => 'Due Amount',
  'COD' => 'COD',
  'first_name_is_required' => 'First name is required',
  'Stores' => 'Stores',
  'Recommended_storeS_list' => 'Recommended storeS list',
  'Want_to_shuffle_the_store_list?' => 'Want to shuffle the store list ',
  'Shuffle_store_when_page_reload?' => 'Shuffle store when page reload ',
  'ex_:_Store_name' => 'Ex : Store name',
  'Ratings' => 'Ratings',
  'Total_Orders' => 'Total Orders',
  'Want_to_remove_the_store_from_the_list?' => 'Want to remove the store from the list ',
  'store_shuffle_status_updated' => 'Store shuffle status updated',
  'Want_to_disable_shuffle_store_list?' => 'Want to disable shuffle store list ',
  'Recommended_Restaurants' => 'Recommended Restaurants',
  'Please_select_a_store' => 'Please select a store',
  'store_recommended_status_updated' => 'Store recommended status updated',
  'Search Stores' => 'Search Stores',
  'store_is_removed_from_the_recommended_list' => 'Store is removed from the recommended list',
  'If_enabled,_store_recommended_section_will_be_shuffled.’' => 'If enabled  store recommended section will be shuffled.’',
  'If_disabled,_store_recommended_section_will_not_be_shuffled.’' => 'If disabled  store recommended section will not be shuffled.’',
  'discount_Bearer' => 'Discount Bearer',
  'Ex_:_50' => 'Ex : 50',
  'store_owner' => 'Store owner',
  'validity' => 'Validity',
  'invalid_distribution_of_discount' => 'Invalid distribution of discount',
  'flash_sale_deleted_successfully' => 'Flash sale deleted successfully',
  'mail' => 'Mail',
  'send_mail' => 'Send mail',
  'a_test_mail_will_be_sent_to_your_email' => 'A test mail will be sent to your email',
  'email_configuration_error' => 'Email configuration error',
  'email_configured_perfectly!' => 'Email configured perfectly!',
  'email_status_is_not_active' => 'Email status is not active',
  'invalid_email_address' => 'Invalid email address',
  'Email Verification' => 'Email Verification',
  'mail_received_successfully' => 'Mail received successfully',
  'customer_wallet_disable_warning_admin' => 'Customer wallet in disabled, please enable it from customer settings.',
  'stock_report' => 'Stock report',
  'bkash_number' => 'Bkash number',
  'account_number' => 'Account number',
  'transaction_number' => 'Transaction number',
  'offline payment' => 'Offline payment',
  'offline_payment' => 'Offline payment',
  'Section_Title' => 'Section Title',
  'Recommended_Stores_List' => 'Recommended Stores List',
  'dsfgsdfg' => 'Dsfgsdfg',
  'Download_Seller_App_From_Appstore' => 'Download Seller App From Appstore',
  'Download_Deliveryman_App_From_Playstore' => 'Download Deliveryman App From Playstore',
  'Download_Deliveryman_App_From_Appstore' => 'Download Deliveryman App From Appstore',
  'browse_web' => 'Browse web',
  'ex_:_search_item_by_name' => 'Ex : search item by name',
  'Define_the_cost_amount_you_want_to_bear_for_this_Flash_Sale.The_total_bear_amount_should_be_100.' => 'Define the cost amount you want to bear for this Flash Sale.The total bear amount should be 100.',
  'Want_to_publish_this_flash_sale?' => 'Want to publish this flash sale ',
  'Want_to_hide_this_flash_sale?' => 'Want to hide this flash sale ',
  'If_you_publish_this_flash_sale,_Customers_can_see_all_stores_&_products_available_under_this_flash_sale_from_the_Customer_App_&_Website._other_flash_sales_will_be_hidden.' => 'If you publish this flash sale  Customers can see all stores & products available under this flash sale from the Customer App & Website. other flash sales will be hidden.',
  'If_you_hide_this_flash_sale,_Customers_Will_NOT_see_all_stores_&_products_available_under_this_flash_sale_from_the_Customer_App_&_Website.' => 'If you hide this flash sale  Customers Will NOT see all stores & products available under this flash sale from the Customer App & Website.',
  'If_you_publish_this_flash_sale,_Customers_can_see_all_stores_&_products_available_under_this_flash_sale_from_the_Customer_App_&_Website._other_flash_sales_will_be_turned_off.' => 'If you publish this flash sale  Customers can see all stores & products available under this flash sale from the Customer App & Website. other flash sales will be turned off.',
  'ex_:_name' => 'Ex : name',
  'Current_Stock' => 'Current Stock',
  'Flash_sale_Qty' => 'Flash sale Qty',
  'Qty_Sold' => 'Qty Sold',
  'Sold_Amount' => 'Sold Amount',
  'zone_id_required' => 'Zone id required',
  'running' => 'Running',
  'expired' => 'Expired',
  'This_feature_is_for_sharing_important_information_or_announcements_related_to_the_store.' => 'This feature is for sharing important information or announcements related to the store.',
  'This_feature_is_for_sharing_important_information_or_announcements_related_to_the_store' => 'This feature is for sharing important information or announcements related to the store',
  'Do_you_want_to_enable_the_announcement' => 'Do you want to enable the announcement ?',
  'Do_you_want_to_disable_the_announcement' => 'Do you want to disable the announcement ?',
  'User_will_able_to_see_the_Announcement_on_the_store_page.' => 'User will able to see the Announcement on the store page.',
  'User_will_not_be_able_to_see_the_Announcement_on_the_store_page' => 'User will not be able to see the Announcement on the store page',
  'store settings updated!' => 'Store settings updated!',
  'The_product_will_be_published_once_it_receives_approval_from_the_admin.' => 'The product will be published once it receives approval from the admin.',
  'Total_stock' => 'Total stock',
  'excluded' => 'Excluded',
  'Banner_title' => 'Banner title',
  'no_more_orders' => 'No more orders',
  'edited' => 'Edited',
  'Update Zone' => 'Update Zone',
  'new_zone' => 'New zone',
  'websocket' => 'Websocket',
  'Messages' => 'Messages',
  'View_Details' => 'View Details',
  'view_order_list' => 'View order list',
  'Start a new message' => 'Start a new message',
  'Too Many Requests' => 'Too Many Requests',
  'payment_settings_updated' => 'Payment settings updated',
  'Ex:Enter_section_title' => 'Ex:Enter section title',
  'Payment_Verified' => 'Payment Verified',
  'prescription_order' => 'Prescription order',
  'Food_Gallery' => 'Food Gallery',
  'New_Food_Request' => 'New Food Request',
  'parcel_orders' => 'Parcel orders',
  'parcel_category' => 'Parcel category',
  'refund_requested' => 'Refund requested',
  'store_data_updated' => 'Store data updated',
  'gst_can_not_be_empty' => 'Gst can not be empty',
  'store_settings_updated' => 'Store settings updated',
  'websocket_settings' => 'Websocket settings',
  'If_WebSocket_is_enabled,_configure_the_server_accordingly_for_optimal_functionality.' => 'If WebSocket is enabled  configure the server accordingly for optimal functionality.',
  'websocket_toggle' => 'Websocket toggle',
  'websocket_?' => 'Websocket  ',
  'If_you_enable_this,_customers_can_choose_websocket_during_checkout.' => 'If you enable this  customers can choose websocket during checkout.',
  'If_you_disable_this,_the_websocket_feature_will_be_hidden.' => 'If you disable this  the websocket feature will be hidden.',
  'websocket_url' => 'Websocket url',
  'Ex_:_ws://178.128.117.0' => 'Ex : ws://178.128.117.0',
  'websocket_port' => 'Websocket port',
  'Ex_:_6001' => 'Ex : 6001',
  'Guest_user' => 'Guest user',
  'Edit_Offline_Payment_Method' => 'Edit Offline Payment Method',
  'you_are_assigned_to_a_order' => 'You are assigned to a order',
  'Add Parcel Category' => 'Add Parcel Category',
  'parcel_category_list' => 'Parcel category list',
  'orders_count' => 'Orders count',
  'update_parcel_category' => 'Update parcel category',
  'parcel_category_updated_successfully' => 'Parcel category updated successfully',
  'Proceed_for_cooking' => 'Proceed for cooking',
  'active_orders' => 'Active orders',
  'delivery_man_details' => 'Delivery man details',
  'attachment' => 'Attachment',
  'ex_search_order_id ' => 'Ex search order id ',
  'sl#' => 'Sl#',
  'deliveryman_earned' => 'Deliveryman earned',
  'deliveryman_updated_successfully' => 'Deliveryman updated successfully',
  'increase_delivery_charge' => 'Increase delivery charge',
  'Set_an_additional_delivery_charge_in_percentage_for_any_emergency_situations._This_amount_will_be_added_to_the_delivery_charge.' => 'Set an additional delivery charge in percentage for any emergency situations. This amount will be added to the delivery charge.',
  'increase_delivery_charge_message' => 'Increase delivery charge message',
  'Customers_will_see_the_delivery_charge_increased_reason_on_the_website_and_customer_app.' => 'Customers will see the delivery charge increased reason on the website and customer app.',
  'Ex:_Rainy_season' => 'Ex: Rainy season',
  'increased_delivery_fee_is_required' => 'Increased delivery fee is required',
  'zone_module_updated_successfully' => 'Zone module updated successfully',
  'Add_Offline_Payment_Method' => 'Add Offline Payment Method',
  'banner_featured_status_updated' => 'Banner featured status updated',
  'select_a_zone' => 'Select a zone',
  'store is required when banner type is store wise' => 'Store is required when banner type is store wise',
  'default_title_is_required' => 'Default title is required',
  'you_can_not_cancel_a_completed_order' => 'You can not cancel a completed order',
  'faield_to_create_order_transaction' => 'Faield to create order transaction',
  'Campaign view' => 'Campaign view',
  'owner' => 'Owner',
  'not_approved' => 'Not approved',
  'you_want_to_confirm_this_store' => 'You want to confirm this store',
  'Approve' => 'Approve',
  'you_want_to_reject_this_store' => 'You want to reject this store',
  'Deny' => 'Deny',
  'store_added_to_campaign' => 'Store added to campaign',
  'want_to_remove_store' => 'Want to remove store',
  'campaign_status_updated' => 'Campaign status updated',
  'guest_checkout' => 'Guest checkout',
  'If_enabled,_customers_do_not_have_to_login_while_checking_out_orders.' => 'If enabled  customers do not have to login while checking out orders.',
  'Want_to_enable_guest_checkout?' => 'Want to enable guest checkout ',
  'Want_to_disable_guest_checkout?' => 'Want to disable guest checkout ',
  'If_you_enable_this,_guest_checkout_will_be_visible_when_customer_is_not_logged_in.' => 'If you enable this  guest checkout will be visible when customer is not logged in.',
  'If_you_disable_this,_guest_checkout_will_not_be_visible_when_customer_is_not_logged_in.' => 'If you disable this  guest checkout will not be visible when customer is not logged in.',
  'Want_to_enable_‘offline_Payment’?' => 'Want to enable ‘offline Payment’ ',
  'If_yes,_Customers_can_choose_the_‘offline_Payment’_option_during_checkout.' => 'If yes  Customers can choose the ‘offline Payment’ option during checkout.',
  'offline_order_accept_message' => 'Offline order accept message',
  'By Turning ON Offline Order ' => 'By Turning ON Offline Order ',
  'accept Message' => 'Accept Message',
  'By Turning OFF Offline Order ' => 'By Turning OFF Offline Order ',
  'User will get a clear message to know that offline order is accepted' => 'User will get a clear message to know that offline order is accepted',
  'User can not get a clear message to know that offline order is accepted or not' => 'User can not get a clear message to know that offline order is accepted or not',
  'offline_order_deny_message' => 'Offline order deny message',
  'deny Message' => 'Deny Message',
  'User will get a clear message to know that offline order is denied' => 'User will get a clear message to know that offline order is denied',
  'User can not get a clear message to know that offline order is denied or not' => 'User can not get a clear message to know that offline order is denied or not',
  'zone_digital_payment_status_updated' => 'Zone digital payment status updated',
  'current_language_key_required' => 'Current language key required',
  'Please do not refresh this page' => 'Please do not refresh this page',
  'Payment' => 'Payment',
  'message_updated' => 'Message updated',
  'zone_offline_payment_status_updated' => 'Zone offline payment status updated',
  'Want_to_disable_‘offline_Payment’?' => 'Want to disable ‘offline Payment’ ',
  'If_yes,_the_offline_payment_option_will_be_hidden_during_checkout.' => 'If yes  the offline payment option will be hidden during checkout.',
  'successfully_added' => 'Successfully added',
  'header_section_updated' => 'Header section updated',
  'bank_info_updated_successfully' => 'Bank info updated successfully',
  'deliveryman_added_successfully' => 'Deliveryman added successfully',
  'added_successfully' => 'Added successfully',
  'already_in_wishlist' => 'Already in wishlist',
  'successfully_removed' => 'Successfully removed',
  'you_are_successfully_removed_from_the_campaign' => 'You are successfully removed from the campaign',
  'addon_added_successfully' => 'Addon added successfully',
  'addon_deleted_successfully' => 'Addon deleted successfully',
  'coupon_added_successfully' => 'Coupon added successfully',
  'coupon_status_updated' => 'Coupon status updated',
  'ssl_commerz' => 'Ssl commerz',
  'get_your_zip_file_from_the_purchased_addon_and_upload_it_and_activate_addon_with_your_Codecanyon_username_and_purchase_code' => 'Get your zip file from the purchased addon and upload it and activate addon with your Codecanyon username and purchase code',
  'now_you’ll_be_successfully_able_to_use_the_addon_for_your_website' => 'Now you’ll be successfully able to use the addon for your website',
  'N:B you_can_upload_only_addons' => 'N:B you can upload only addons',
  'upload_addon' => 'Upload addon',
  'Page Expired' => 'Page Expired',
  'parcel_settings' => 'Parcel settings',
  'deliveryman_commission' => 'Deliveryman commission',
  'add_new_coupon' => 'Add new coupon',
  'Ex :_Search by title or code' => 'Ex : Search by title or code',
  'No data to show' => 'No data to show',
  'Create_Role' => 'Create Role',
  'role_form' => 'Role form',
  'Employees' => 'Employees',
  'search_role' => 'Search role',
  'Role name is required!' => 'Role name is required!',
  'Please select atleast one module' => 'Please select atleast one module',
  'role_added_successfully' => 'Role added successfully',
  'store setup' => 'Store setup',
  'my shop' => 'My shop',
  'employee_list' => 'Employee list',
  'Search by name or email..' => 'Search by name or email..',
  'employee_image' => 'Employee image',
  'Ratio (1:1)' => 'Ratio (1:1)',
  'Employee image size max 2 MB' => 'Employee image size max 2 MB',
  'vendor_employee' => 'Vendor employee',
  'welcome' => 'Welcome',
  'employee_welcome_message' => 'Employee welcome message',
  'employee_deleted_successfully' => 'Employee deleted successfully',
  'Bank Info View' => 'Bank Info View',
  'Add Bank Info' => 'Add Bank Info',
  'edit_bank_info' => 'Edit bank info',
  'branch_name' => 'Branch name',
  'bKash' => 'BKash',
  ' Special Criteria' => ' Special Criteria',
  ' Special review' => ' Special review',
  '*If_the_Admin_enables_the_âRefund_Request_Modeâ,_customers_can_request_a_refund.' => '*If the Admin enables the âRefund Request Modeâ  customers can request a refund.',
  '*Users_cannot_cancel_an_order_if_the_Admin_does_not_specify_a_cause_for_cancellation,_even_though_they_see_the_âCancel_Orderâ_option._So_Admin_MUST_provide_a_proper_Order_Cancellation_Reason_and_select_the_related_user.' => '*Users cannot cancel an order if the Admin does not specify a cause for cancellation  even though they see the âCancel Orderâ option. So Admin MUST provide a proper Order Cancellation Reason and select the related user.',
  'If_enabled_Customers_will_be_able_to_select_COD_as_a_payment_method_during_checkout' => 'If enabled Customers will be able to select COD as a payment method during checkout',
  'If_enabled_Customers_will_be_able_to_select_digital_payment_as_a_payment_method_during_checkout' => 'If enabled Customers will be able to select digital payment as a payment method during checkout',
  'If_enabled_Customers_will_be_able_to_select_offline_payment_as_a_payment_method_during_checkout' => 'If enabled Customers will be able to select offline payment as a payment method during checkout',
  'config_data_updated' => 'Config data updated',
  'Please check the recaptcha' => 'Please check the recaptcha',
  'Ex_:_Enter_Title' => 'Ex : Enter Title',
  'Ex_:_Enter_Subtitle' => 'Ex : Enter Subtitle',
  'Select Restaurant' => 'Select Restaurant',
  'addon_update' => 'Addon update',
  'Store_Campaign_Request' => 'Store Campaign Request',
  'If_enabled,_customers_will_get_an_automatic_confirmation_mail_for_successful_Order_Placement_with_an_invoice' => 'If enabled  customers will get an automatic confirmation mail for successful Order Placement with an invoice',
  '#_48573' => '# 48573',
  '23 Jul, 2023 4:30 am' => '23 Jul  2023 4:30 am',
  'Munam_Shahariar' => 'Munam Shahariar',
  '4517_Washington_Ave._Manchester,_Kentucky_39495' => '4517 Washington Ave. Manchester  Kentucky 39495',
  '1._The_school_of_life_-_emotional_baggage_tote_bag_-_canvas_tote_bag_(navy)_x_1' => '1. The school of life - emotional baggage tote bag - canvas tote bag (navy) x 1',
  '$5,465' => '$5 465',
  '2._3USB_Head_Phone_x_1' => '2. 3USB Head Phone x 1',
  '$354' => '$354',
  'Item_Price' => 'Item Price',
  '$85' => '$85',
  'Addon' => 'Addon',
  'Sub_total' => 'Sub total',
  '$90' => '$90',
  '$10' => '$10',
  'Coupon_Discount' => 'Coupon Discount',
  '$00' => '$00',
  'VAT_/_Tax' => 'VAT / Tax',
  '$15' => '$15',
  'Delivery_Charge' => 'Delivery Charge',
  '$20' => '$20',
  'Total' => 'Total',
  '1$05' => '1$05',
  'generated_link' => 'Generated link',
  'If_enabled,_customers_will_receive_a_confirmation_email_that_their_registration_was_successfull.' => 'If enabled  customers will receive a confirmation email that their registration was successfull.',
  'Are_you_sure_you_want_to_remove_this_image_?' => 'Are you sure you want to remove this image ?',
  'By Turning ON' => 'By Turning ON',
  'seller_links_updated' => 'Seller links updated',
  'delivery_man_links_updated' => 'Delivery man links updated',
  'Want_to_disable_the_App_Store_button_for_Store_App' => 'Want to disable the App Store button for Store App',
  '(size:_1:1)' => '(size: 1:1)',
  'feature_status_updated' => 'Feature status updated',
  'feature_deleted_successfully' => 'Feature deleted successfully',
  'background_updated' => 'Background updated',
  'This section will be disabled. You can enable it in the settings.' => 'This section will be disabled. You can enable it in the settings.',
  'review_status_updated' => 'Review status updated',
  'review_deleted_successfully' => 'Review deleted successfully',
  '(1:1)' => '(1:1)',
  'Want to enable this feature?' => 'Want to enable this feature ',
  'Want to disable this feature?' => 'Want to disable this feature ',
  'It will be available on the landing page.' => 'It will be available on the landing page.',
  'It will be hidden from the landing page.' => 'It will be hidden from the landing page.',
  'criteria_status_updated' => 'Criteria status updated',
  'criteria_deleted_successfully' => 'Criteria deleted successfully',
  'criteria_updated_successfully' => 'Criteria updated successfully',
  'By Turning ON Promotional Banner Section' => 'By Turning ON Promotional Banner Section',
  'want_to_enable_the_app_store_button_for_user_app?' => 'Want to enable the app store button for user app ',
  'want_to_disable_the_app_store_button_for_user_app?' => 'Want to disable the app store button for user app ',
  'if_enabled,_the_user_app_download_button_will_be_visible_on_the_landing_page.' => 'If enabled  the user app download button will be visible on the landing page.',
  'if_disabled,_this_button_will_be_hidden_from_the_landing_page.' => 'If disabled  this button will be hidden from the landing page.',
  'download_app_section_updated' => 'Download app section updated',
  'want_to_enable' => 'Want to enable',
  'want_to_disable' => 'Want to disable',
  'playstore_button_enabled_for_seller' => 'Playstore button enabled for seller',
  'playstore_button_disabled_for_seller' => 'Playstore button disabled for seller',
  'Playstore_button_is_enabled_now_everyone_can_use_or_see_the_button' => 'Playstore button is enabled now everyone can use or see the button',
  'Playstore_button_is_disabled_now_no_one_can_use_or_see_the_button' => 'Playstore button is disabled now no one can use or see the button',
  'app_store_button_enabled_for_seller' => 'App store button enabled for seller',
  'app_store_button_disabled_for_seller' => 'App store button disabled for seller',
  'playstore_button_enabled_for_delivery_man' => 'Playstore button enabled for delivery man',
  'playstore_button_disabled_for_delivery_man' => 'Playstore button disabled for delivery man',
  'app_store_button_enabled_for_delivery_man' => 'App store button enabled for delivery man',
  'app_store_button_disabled_for_delivery_man' => 'App store button disabled for delivery man',
  'App_Store_button_is_enabled_now_everyone_can_use_or_see_the_button' => 'App Store button is enabled now everyone can use or see the button',
  'App_Store_button_is_disabled_now_no_one_can_use_or_see_the_button' => 'App Store button is disabled now no one can use or see the button',
  'want_to_enable_the_play_store_button_for_user_app' => 'Want to enable the play store button for user app',
  'want_to_disable_the_play_store_button_for_user_app' => 'Want to disable the play store button for user app',
  'if_enabled,_the_user_app_download_button_will_be_visible_on_react_landing_page' => 'If enabled  the user app download button will be visible on react landing page',
  'if_disabled,_this_button_will_be_hidden_from_the_react_landing_page' => 'If disabled  this button will be hidden from the react landing page',
  'want_to_enable_the_app_store_button_for_user_app' => 'Want to enable the app store button for user app',
  'want_to_disable_the_app_store_button_for_user_app' => 'Want to disable the app store button for user app',
  'Want_to_delete_this_banner' => 'Want to delete this banner ?',
  'by_turning_on_this_review' => 'By turning on this review',
  'this_review' => 'This review',
  'by_turning_off_this_review' => 'By turning off this review',
  'this_section_will_be_enabled_you_can_see_this_section_on_your_landing_page' => 'This section will be enabled you can see this section on your landing page',
  'this_section_will_be_disabled_you_can_enable_it_in_the_settings' => 'This section will be disabled you can enable it in the settings',
  'category_updated_successfully' => 'Category updated successfully',
  'admin_employee' => 'Admin employee',
  'disbursement_method' => 'Disbursement method',
  'disbursement_report' => 'Disbursement report',
  'thursday' => 'Thursday',
  'edit_store_info' => 'Edit store info',
  'contact_number' => 'Contact number',
  'upload_logo' => 'Upload logo',
  'disbursement' => 'Disbursement',
  'Cash_In_Hand_Overflow' => 'Cash In Hand Overflow',
  'If_enabled,_delivery_men_will_be_automatically_suspended_by_the_system_when_their_‘Cash_in_Hand’_limit_is_exceeded.' => 'If enabled  delivery men will be automatically suspended by the system when their ‘Cash in Hand’ limit is exceeded.',
  'cash_in_hand_overflow' => 'Cash in hand overflow',
  'If_enabled,_delivery_men_have_to_provide_collected_cash_by_them_self' => 'If enabled  delivery men have to provide collected cash by them self',
  'If_disabled,_delivery_men_do_not_have_to_provide_collected_cash_by_them_self' => 'If disabled  delivery men do not have to provide collected cash by them self',
  'Delivery_Man_Maximum_Cash_in_Hand' => 'Delivery Man Maximum Cash in Hand',
  'Deliveryman_can_not_accept_any_orders_when_the_Cash_In_Hand_limit_exceeds_and_must_deposit_the_amount_to_the_admin_before_accepting_new_orders' => 'Deliveryman can not accept any orders when the Cash In Hand limit exceeds and must deposit the amount to the admin before accepting new orders',
  'Minimum_Amount_To_Pay' => 'Minimum Amount To Pay',
  'Enter_the_minimum_cash_amount_delivery_men_can_pay' => 'Enter the minimum cash amount delivery men can pay',
  'denied_delivery_man' => 'Denied delivery man',
  'disbursements' => 'Disbursements',
  'store_disbursement' => 'Store disbursement',
  'dm_disbursement' => 'Dm disbursement',
  'delivery_man_disbursement' => 'Delivery man disbursement',
  'withdraw_method' => 'Withdraw method',
  'wallet_adjusted_partially' => 'Wallet adjusted partially',
  'Balance' => 'Balance',
  'Total_withdrawn' => 'Total withdrawn',
  'Pending_withdraw' => 'Pending withdraw',
  'Store_Disbursement' => 'Store Disbursement',
  'partially_completed' => 'Partially completed',
  'view_details' => 'View details',
  'Deliveryman_Disbursement' => 'Deliveryman Disbursement',
  'Disbursement_Details' => 'Disbursement Details',
  'select_delivery_man' => 'Select delivery man',
  'all_delivery_man' => 'All delivery man',
  'select_payment_method' => 'Select payment method',
  'all_payment_methods' => 'All payment methods',
  'Total_Disbursements' => 'Total Disbursements',
  'search_by_delivery_man_info' => 'Search by delivery man info',
  'complete' => 'Complete',
  'Delivery_Man_Info' => 'Delivery Man Info',
  'Disburse_Amount' => 'Disburse Amount',
  'Payment_method' => 'Payment method',
  'Disbursement_ID' => 'Disbursement ID',
  'Delivery_Man_Information' => 'Delivery Man Information',
  'Account_Information' => 'Account Information',
  'account_name' => 'Account name',
  'image_uploaded_successfully' => 'Image uploaded successfully',
  'image_deleted_successfully' => 'Image deleted successfully',
  'message' => 'Message',
  'message_description' => 'Message description',
  'select_a_vehicle' => 'Select a vehicle',
  'select_dm_type' => 'Select dm type',
  'Withdraw_Able_Balance' => 'Withdraw Able Balance',
  'delivery_setup' => 'Delivery setup',
  'click_to_edit_this_item' => 'Click to edit this item',
  'you_are_unassigned_from_a_order' => 'You are unassigned from a order',
  'search_by_store_info' => 'Search by store info',
  'Store_Info' => 'Store Info',
  'Store_Information' => 'Store Information',
  'Owner_Information' => 'Owner Information',
  'disbursement_method_list' => 'Disbursement method list',
  'wallet_method_list' => 'Wallet method list',
  'Add_method' => 'Add method',
  'methods' => 'Methods',
  'Search_Method_Name' => 'Search Method Name',
  'method_fields' => 'Method fields',
  'default_method' => 'Default method',
  'string' => 'String',
  'Placeholder' => 'Placeholder',
  'number' => 'Number',
  'Optional' => 'Optional',
  'Default_Method_updated_successfully' => 'Default Method updated successfully',
  'Default_Method_updated_failed.' => 'Default Method updated failed.',
  'After purchasing the Payment & SMS Module from Codecanyon, you will find a file download option.' => 'After purchasing the Payment & SMS Module from Codecanyon  you will find a file download option.',
  'Download the file. It will be downloaded as Zip format Filename.Zip.' => 'Download the file. It will be downloaded as Zip format Filename.Zip.',
  'Extract the file and you will get another file name payment.zip.' => 'Extract the file and you will get another file name payment.zip.',
  'Upload the file here and your Addon uploading is complete !' => 'Upload the file here and your Addon uploading is complete !',
  'Then active the Addon and setup all the options. you are good to go !' => 'Then active the Addon and setup all the options. you are good to go !',
  'Withdrawal_Methods' => 'Withdrawal Methods',
  'Ex:_Bank' => 'Ex: Bank',
  'Input_Field_Type' => 'Input Field Type',
  'Text' => 'Text',
  'Number' => 'Number',
  'Date' => 'Date',
  'field_name' => 'Field name',
  'Ex:_Account_name' => 'Ex: Account name',
  'placeholder_text' => 'Placeholder text',
  'Ex:_John' => 'Ex: John',
  'Is_required_' => 'Is required ',
  'Add_Fields' => 'Add Fields',
  'Reached_maximum' => 'Reached maximum',
  'Is_required_?' => 'Is required  ',
  'If_you_enable_it,_customers_will_see_the_product_Price_including_Tax,_during_checkout.' => 'If you enable it  customers will see the product Price including Tax  during checkout.',
  'If_enabled,_stores_will_be_automatically_suspended_by_the_system_when_their_‘Cash_in_Hand’_limit_is_exceeded.' => 'If enabled  stores will be automatically suspended by the system when their ‘Cash in Hand’ limit is exceeded.',
  'If_enabled,_stores_have_to_provide_collected_cash_by_them_self' => 'If enabled  stores have to provide collected cash by them self',
  'If_disabled,_stores_do_not_have_to_provide_collected_cash_by_them_self' => 'If disabled  stores do not have to provide collected cash by them self',
  'Maximum_Amount_to_Hold_Cash_in_Hand' => 'Maximum Amount to Hold Cash in Hand',
  'Enter_the_maximum_cash_amount_stores_can_hold._If_this_number_exceeds,_stores_will_be_suspended_and_not_receive_any_orders.' => 'Enter the maximum cash amount stores can hold. If this number exceeds  stores will be suspended and not receive any orders.',
  'Enter_the_minimum_cash_amount_stores_can_pay' => 'Enter the minimum cash amount stores can pay',
  'attribute_choice_option_value_can_not_be_null' => 'Attribute choice option value can not be null',
  'please_add_more_options_or_change_the_max_value_for' => 'Please add more options or change the max value for',
  'Start Date' => 'Start Date',
  'End Date' => 'End Date',
  'Disbursement_Report' => 'Disbursement Report',
  'Store_Disbursements' => 'Store Disbursements',
  'Delivery_Man_Disbursements' => 'Delivery Man Disbursements',
  'Pending_Disbursements' => 'Pending Disbursements',
  'All_the_pending_disbursement_requests_that_require_admin’s_action_(complete/cancel).' => 'All the pending disbursement requests that require admin’s action (complete/cancel).',
  'Completed_Disbursements' => 'Completed Disbursements',
  'The_amount_of_disbursement_is_completed.' => 'The amount of disbursement is completed.',
  'Canceled_Transactions' => 'Canceled Transactions',
  'See_all_the_canceled_disbursement_amounts_here.' => 'See all the canceled disbursement amounts here.',
  'Search_Data' => 'Search Data',
  'All_Payment_Method' => 'All Payment Method',
  'select_status' => 'Select status',
  'All_status' => 'All status',
  'All_Time' => 'All Time',
  'Previous_Year' => 'Previous Year',
  'search_by_id' => 'Search by id',
  'all_delivery_mans' => 'All delivery mans',
  'Store Wise Report' => 'Store Wise Report',
  'Filter Data' => 'Filter Data',
  'total_order_amount' => 'Total order amount',
  'Total Discount Given' => 'Total Discount Given',
  'Product Discount' => 'Product Discount',
  'Average Value of all type of orders.' => 'Average Value of all type of orders.',
  'order statistics' => 'Order statistics',
  'Total_canceled' => 'Total canceled',
  'Total_ongoing' => 'Total ongoing',
  'Total_delivered' => 'Total delivered',
  'View All Orders' => 'View All Orders',
  'Total Sales' => 'Total Sales',
  'Search by ID..' => 'Search by ID..',
  'Order ID' => 'Order ID',
  'Order Date' => 'Order Date',
  'Customer Info' => 'Customer Info',
  'Tax' => 'Tax',
  'Delivery Charge' => 'Delivery Charge',
  'Total canceled' => 'Total canceled',
  'Total ongoing' => 'Total ongoing',
  'Total delivered' => 'Total delivered',
  'Total Tax' => 'Total Tax',
  'Total Commission' => 'Total Commission',
  'Total Store Earnings' => 'Total Store Earnings',
  'Search by product..' => 'Search by product..',
  'QTY Sold' => 'QTY Sold',
  'Discount Given' => 'Discount Given',
  'Day' => 'Day',
  'If_disabled,_store_recommended_section_will_not_be_shuffled.' => 'If disabled  store recommended section will not be shuffled.',
  'store_earned' => 'Store earned',
  'admin_earned' => 'Admin earned',
  'Last name is required!' => 'Last name is required!',
  'admin_updated_successfully' => 'Admin updated successfully',
  'total_sell' => 'Total sell',
  'delivery_commission' => 'Delivery commission',
  'currency' => 'Currency',
  'Disbursement_settings' => 'Disbursement settings',
  'Disbursement_Request_Type' => 'Disbursement Request Type',
  'Choose_Manual_or_Automated_Disbursement_Requests._In_Automated_mode,_withdrawal_requests_for_disbursement_are_generated_automatically;_in_Manual_mode,_stores_need_to_request_withdrawals_manually.' => 'Choose Manual or Automated Disbursement Requests. In Automated mode  withdrawal requests for disbursement are generated automatically  in Manual mode  stores need to request withdrawals manually.',
  'automated' => 'Automated',
  'System_PHP_Path' => 'System PHP Path',
  'Default_location_where_the_PHP_executable_is_installed_on_server.' => 'Default location where the PHP executable is installed on server.',
  'Ex:_/usr/bin/php' => 'Ex: /usr/bin/php',
  'Store_Panel' => 'Store Panel',
  'Create_Disbursements' => 'Create Disbursements',
  'Choose_how_the_disbursement_request_will_be_generated:_Monthly,_Weekly_or_Daily.' => 'Choose how the disbursement request will be generated: Monthly  Weekly or Daily.',
  'daily' => 'Daily',
  'weekly' => 'Weekly',
  'monthly' => 'Monthly',
  'Week_Start' => 'Week Start',
  'Choose_when_the_week_starts_for_the_new_disbursement_request._This_section_will_only_appear_when_weekly_disbursement_is_selected.' => 'Choose when the week starts for the new disbursement request. This section will only appear when weekly disbursement is selected.',
  'Create_Time' => 'Create Time',
  'Define_when_the_new_disbursement_request_will_be_generated_automatically.' => 'Define when the new disbursement request will be generated automatically.',
  'Ex:_7' => 'Ex: 7',
  'Minimum_Amount' => 'Minimum Amount',
  'Enter_the_minimum_amount_to_be_eligible_for_generating_an_auto-disbursement_request.' => 'Enter the minimum amount to be eligible for generating an auto-disbursement request.',
  'Days_needed_to_complete_disbursement' => 'Days needed to complete disbursement',
  'Enter_the_number_of_days_in_which_the_disbursement_will_be_completed.' => 'Enter the number of days in which the disbursement will be completed.',
  'Delivery_man' => 'Delivery man',
  'Cron_Command_for_Disbursement' => 'Cron Command for Disbursement',
  'In_some_server_configurations,_the_exec_function_in_PHP_may_not_be_enabled,_limiting_your_ability_to_create_cron_jobs_programmatically._A_cron_job_is_a_scheduled_task_that_automates_repetitive_processes_on_your_server._However,_if_the_exec_function_is_disabled,_you_can_manually_set_up_cron_jobs_using_the_following_commands' => 'In some server configurations  the exec function in PHP may not be enabled  limiting your ability to create cron jobs programmatically. A cron job is a scheduled task that automates repetitive processes on your server. However  if the exec function is disabled  you can manually set up cron jobs using the following commands',
  'Store_Cron_Command' => 'Store Cron Command',
  'Delivery_Man_Cron_Command' => 'Delivery Man Cron Command',
  'successfully_updated_disbursement_functionality' => 'Successfully updated disbursement functionality',
  'Check_Dependencies' => 'Check Dependencies',
  'you_want_to_update_user_info' => 'You want to update user info',
  'want_to_update_password' => 'Want to update password',
  'profile_updated_successfully' => 'Profile updated successfully',
  'Cancele_ Transactions' => 'Cancele  Transactions',
  'store_wallet' => 'Store wallet',
  'disbursement_method_setup' => 'Disbursement method setup',
  'disbursement_methods' => 'Disbursement methods',
  'Ex : Search by name' => 'Ex : Search by name',
  'add_new_method' => 'Add new method',
  'payment_info' => 'Payment info',
  'add_method' => 'Add method',
  'Select_Disburse_Method' => 'Select Disburse Method',
  'Cash_in_Hand' => 'Cash in Hand',
  'The_total_amount_you’ve_received_from_the_customer_in_cash_(Cash_on_Delivery)' => 'The total amount you’ve received from the customer in cash (Cash on Delivery)',
  'Payable_Balance' => 'Payable Balance',
  'Total_Withdrawn' => 'Total Withdrawn',
  'withdraw_request' => 'Withdraw request',
  'Select_Withdraw_Method' => 'Select Withdraw Method',
  'Payment_history' => 'Payment history',
  'Next_Payouts' => 'Next Payouts',
  'Transaction_Type' => 'Transaction Type',
  'Pay_Via_Online' => 'Pay Via Online',
  'Faster_&_secure_way_to_pay_bill' => 'Faster & secure way to pay bill',
  'Proceed' => 'Proceed',
  'Adjust_Wallet' => 'Adjust Wallet',
  'This_will_adjust_the_collected_cash_on_your_earning' => 'This will adjust the collected cash on your earning',
  'Payment_Time' => 'Payment Time',
  'Delivery Information' => 'Delivery Information',
  'customer_not_selected' => 'Customer not selected',
  'Cart' => 'Cart',
  'Please choose all the options' => 'Please choose all the options',
  'Billing Section' => 'Billing Section',
  'walk_in_customer' => 'Walk in customer',
  'extra_discount' => 'Extra discount',
  'Cash' => 'Cash',
  'Card' => 'Card',
  'Paid Amount' => 'Paid Amount',
  'Change Amount' => 'Change Amount',
  'payment' => 'Payment',
  'Ex: Jhone' => 'Ex: Jhone',
  'Ex: 4th' => 'Ex: 4th',
  'Ex: 45/C' => 'Ex: 45/C',
  'Ex: 1A' => 'Ex: 1A',
  'Your browser doesn\'t support geolocation' => 'Your browser doesn t support geolocation',
  'Sorry, stock limit exceeded.' => 'Sorry  stock limit exceeded.',
  'Item Campaign Preview' => 'Item Campaign Preview',
  'campaign_starts_from' => 'Campaign starts from',
  'campaign_ends_at' => 'Campaign ends at',
  'Addons' => 'Addons',
  'update_vehicle_category' => 'Update vehicle category',
  'Vehicle_type' => 'Vehicle type',
  'ex_:_bike' => 'Ex : bike',
  'extra_charges' => 'Extra charges',
  'This amount will be added with delivery charge' => 'This amount will be added with delivery charge',
  'starting_coverage_area' => 'Starting coverage area',
  'minimum_coverage_area_hint' => 'Minimum coverage area hint',
  'maximum_coverage_area' => 'Maximum coverage area',
  'maximum_coverage_area_hint' => 'Maximum coverage area hint',
  'Vehicle_category_updated' => 'Vehicle category updated',
  'confirm_this_order_?' => 'Confirm this order  ',
  'This report will show all the orders in which the store discount has been used. The store discounts are: Free delivery, Coupon discount & item discounts(partial according to order commission).' => 'This report will show all the orders in which the store discount has been used. The store discounts are: Free delivery  Coupon discount & item discounts(partial according to order commission).',
  'customers_reviews' => 'Customers reviews',
  'Unadjusted' => 'Unadjusted',
  'Adjust_with_wallet' => 'Adjust with wallet',
  'Adjust_the_withdrawable_balance_&_unadjusted_balance_with_your_wallet_(Cash_in_Hand)_or_click_‘Request_Withdraw’' => 'Adjust the withdrawable balance & unadjusted balance with your wallet (Cash in Hand) or click ‘Request Withdraw’',
  'request_withdraw' => 'Request withdraw',
  'As_you_have_more_‘Withdrawable_Balance’_than_‘Cash_in_Hand’,_you_need_to_request_for_withdrawal_from_Admin' => 'As you have more ‘Withdrawable Balance’ than ‘Cash in Hand’  you need to request for withdrawal from Admin',
  'search_by_disbursement_id' => 'Search by disbursement id',
  'Disbursement_List' => 'Disbursement List',
  'If you disable this, the Deliveryman will deliver the order and update the status. He doesn’t need to verify the order with any code.' => 'If you disable this  the Deliveryman will deliver the order and update the status. He doesn’t need to verify the order with any code.',
  'If you enable this, customers can place an order by simply uploading their prescriptions in the Pharmacy module from the Customer App or Website. Stores can enable/disable this feature from store settings if needed.' => 'If you enable this  customers can place an order by simply uploading their prescriptions in the Pharmacy module from the Customer App or Website. Stores can enable/disable this feature from store settings if needed.',
  'If disabled, this feature will be hidden from the Customer App, Website, and Store App & Panel.' => 'If disabled  this feature will be hidden from the Customer App  Website  and Store App & Panel.',
  'If you enable this, customers can use the Home Delivery Option during checkout from the Customer App or Website.' => 'If you enable this  customers can use the Home Delivery Option during checkout from the Customer App or Website.',
  'If you disable this, the Home Delivery feature will be hidden from the customer app and website.' => 'If you disable this  the Home Delivery feature will be hidden from the customer app and website.',
  'If you enable this, customers can use the Takeaway feature during checkout from the Customer App or Website.' => 'If you enable this  customers can use the Takeaway feature during checkout from the Customer App or Website.',
  'If you disable this, the Takeaway feature will be hidden from the Customer App or Website.' => 'If you disable this  the Takeaway feature will be hidden from the Customer App or Website.',
  'If you enable this, customers can choose a suitable delivery schedule during checkout.' => 'If you enable this  customers can choose a suitable delivery schedule during checkout.',
  'If you disable this, the Scheduled Delivery feature will be hidden.' => 'If you disable this  the Scheduled Delivery feature will be hidden.',
  'Order Cancellation Messages' => 'Order Cancellation Messages',
  'Transaction_ID' => 'Transaction ID',
  '23984357sd834' => '23984357sd834',
  '23 Jul, 2023 3:34 am' => '23 Jul  2023 3:34 am',
  '$500' => '$500',
  'By Turning ON Offline Payment Option' => 'By Turning ON Offline Payment Option',
  'By Turning OFF Offline Payment Option' => 'By Turning OFF Offline Payment Option',
  'Customers will not be able to select Offline Payment as a payment method during checkout. Please review your settings and enable Offline Payment if you wish to offer this payment option to customers.' => 'Customers will not be able to select Offline Payment as a payment method during checkout. Please review your settings and enable Offline Payment if you wish to offer this payment option to customers.',
  'Customers will be able to select Offline Payment as a payment method during checkout.' => 'Customers will be able to select Offline Payment as a payment method during checkout.',
  'By Turning ON Order' => 'By Turning ON Order',
  'By Turning OFF Order' => 'By Turning OFF Order',
  'User will get a clear message to know that the order is pending.' => 'User will get a clear message to know that the order is pending.',
  'User cannot get a clear message to know that the order is pending or not.' => 'User cannot get a clear message to know that the order is pending or not.',
  'User will get a clear message to know that the order is confirmed.' => 'User will get a clear message to know that the order is confirmed.',
  'User cannot get a clear message to know that the order is confirmed or not.' => 'User cannot get a clear message to know that the order is confirmed or not.',
  'User will get a clear message to know that the order is processing.' => 'User will get a clear message to know that the order is processing.',
  'User cannot get a clear message to know that the order is processing or not.' => 'User cannot get a clear message to know that the order is processing or not.',
  'User will get a clear message to know that the order is handovered.' => 'User will get a clear message to know that the order is handovered.',
  'User cannot get a clear message to know that the order is handovered or not.' => 'User cannot get a clear message to know that the order is handovered or not.',
  'User will get a clear message to know that the order is out for delivery.' => 'User will get a clear message to know that the order is out for delivery.',
  'User cannot get a clear message to know that the order is out for delivery or not.' => 'User cannot get a clear message to know that the order is out for delivery or not.',
  'User will get a clear message to know that the order is delivered.' => 'User will get a clear message to know that the order is delivered.',
  'User cannot get a clear message to know that the order is delivered or not.' => 'User cannot get a clear message to know that the order is delivered or not.',
  'User will get a clear message to know that the order is assigned to a delivery man.' => 'User will get a clear message to know that the order is assigned to a delivery man.',
  'User cannot get a clear message to know that the order is assigned to a delivery man or not.' => 'User cannot get a clear message to know that the order is assigned to a delivery man or not.',
  'User will get a clear message to know that the order is delivered by a delivery man.' => 'User will get a clear message to know that the order is delivered by a delivery man.',
  'User cannot get a clear message to know that the order is delivered by a delivery man or not.' => 'User cannot get a clear message to know that the order is delivered by a delivery man or not.',
  'User will get a clear message to know that the order is canceled.' => 'User will get a clear message to know that the order is canceled.',
  'User cannot get a clear message to know that the order is canceled or not.' => 'User cannot get a clear message to know that the order is canceled or not.',
  'User will get a clear message to know that the order is refunded.' => 'User will get a clear message to know that the order is refunded.',
  'User cannot get a clear message to know that the order is refunded or not.' => 'User cannot get a clear message to know that the order is refunded or not.',
  'User will get a clear message to know that the order\'s refund request is canceled.' => 'User will get a clear message to know that the order s refund request is canceled.',
  'User cannot get a clear message to know that the order\'s refund request is canceled or not.' => 'User cannot get a clear message to know that the order s refund request is canceled or not.',
  'By Turning ON Offline Order' => 'By Turning ON Offline Order',
  'By Turning OFF Offline Order' => 'By Turning OFF Offline Order',
  'User will get a clear message to know that the offline order is accepted.' => 'User will get a clear message to know that the offline order is accepted.',
  'User cannot get a clear message to know that the offline order is accepted or not.' => 'User cannot get a clear message to know that the offline order is accepted or not.',
  'User will get a clear message to know that the offline order is denied.' => 'User will get a clear message to know that the offline order is denied.',
  'User cannot get a clear message to know that the offline order is denied or not.' => 'User cannot get a clear message to know that the offline order is denied or not.',
  'Ex: my-awesome-app.firebase.com' => 'Ex: my-awesome-app.firebase.com',
  'Ex: my-awesome-app.apps.com' => 'Ex: my-awesome-app.apps.com',
  'category_added_successfully' => 'Category added successfully',
  'campaign_deleted_successfully' => 'Campaign deleted successfully',
  'Do_not_Logout' => 'Don’t Logout',
  'category_priority_updated successfully' => 'Category priority updated successfully',
  'category_status_updated' => 'Category status updated',
  'category_featured_updated' => 'Category featured updated',
  'video_url' => 'Video url',
  'upload_video' => 'Upload video',
  'Browse_file"' => 'Browse file ',
  'Video_Size_Max_2MB' => 'Video Size Max 2MB',
  'Video_format_:_MP4_,_WebM_,_Ogg' => 'Video format : MP4   WebM   Ogg',
  'Video' => 'Video',
  'Video_Size_Max_5MB' => 'Video Size Max 5MB',
  'The banner video content must not be greater than 5120 kilobytes.' => 'The banner video content must not be greater than 5120 kilobytes.',
  'user_not_found' => 'User not found',
  'Customer will not see loyalty point option from his profile settings' => 'Customer will not see loyalty point option from his profile settings',
  'filter_criteria' => 'Filter criteria',
  'unauthorized' => 'Unauthorized',
  'Store_wallet_adjustment_partial' => 'Store wallet adjustment partial',
  'store_collect_cash_payments' => 'Store collect cash payments',
  'deliveryman_collect_cash_payments' => 'Deliveryman collect cash payments',
  '7i96' => '7i96',
  12 => '12',
  'If_enabled,_Stores_will_receive_an_automated_confirmation_mail_that_their_join_request_is_successful' => 'If enabled  Stores will receive an automated confirmation mail that their join request is successful',
  'Withdrawable_Balance' => 'Withdrawable Balance',
  'Withdraw_methods_are_not_available' => 'Withdraw methods are not available',
  'Default_method' => 'Default method',
  'Wallet_Adjustment' => 'Wallet Adjustment',
  'Store_wallet_adjustment_full' => 'Store wallet adjustment full',
  'Withdraw_Request' => 'Withdraw Request',
  'Want to delete this  ?' => 'Want to delete this   ',
  'you_want_to_unblock_this_customer' => 'You want to unblock this customer',
  'Self_Delivery_is_Disable' => 'Self Delivery is Disable',
  'distance' => 'Distance',
  'receiver_info' => 'Receiver info',
  'sender' => 'Sender',
  'Ride' => 'Ride',
  'test module' => 'Test module',
  'retre' => 'Retre',
  'test test' => 'Test test',
  'As_you_have_more_‘Cash_in_Hand’_than_‘Withdrawable_Balance,’_you_need_to_pay_the_Admin' => 'As you have more ‘Cash in Hand’ than ‘Withdrawable Balance ’ you need to pay the Admin',
  'Pay_Now' => 'Pay Now',
  'Adjust_the_payable_&_withdrawable_balance_with_your_wallet_(Cash_in_Hand)_or_click_‘Pay_Now’.' => 'Adjust the payable & withdrawable balance with your wallet (Cash in Hand) or click ‘Pay Now’.',
  'disbursement_method_details' => 'Disbursement method details',
  'search_by_ID' => 'Search by ID',
  'ID' => 'ID',
  'Created_at' => 'Created at',
  'Payout_Date' => 'Payout Date',
  'Estimated' => 'Estimated',
  'prescription' => 'Prescription',
  'order_attachment' => 'Order attachment',
  'Thursday' => 'Thursday',
  '--' => '--',
  'alert_store_join_campaign' => 'Alert store join campaign',
  'join' => 'Join',
  'item_campaign' => 'Item campaign',
  'Search by title' => 'Search by title',
  'You_need_to_select_minimum_ ' => 'You need to select minimum  ',
  'to_maximum_ ' => 'To maximum  ',
  'category_id' => 'Category id',
  'category_name' => 'Category name',
  'Want_to_delete_this_addon_?' => 'Want to delete this addon  ',
  'default_meta_title_is_required' => 'Default meta title is required',
  'default_meta_description_is_required' => 'Default meta description is required',
  'meta_data_updated' => 'Meta data updated',
  'Currently,_there_are_no_payment_options_available._Please_contact_admin_regarding_any_payment_process_or_queries.' => 'Currently  there are no payment options available. Please contact admin regarding any payment process or queries.',
  'youtube_video_url' => 'Youtube video url',
  'YouTube_Video_URL' => 'YouTube Video URL',
  'Enter_YouTube_URL' => 'Enter YouTube URL',
  'Enter_YouTube_Video_URL' => 'Enter YouTube Video URL',
  'Video_format_:_MP4' => 'Video format : MP4',
  'business_module_data_is_required' => 'Business module data is required',
  'zone_deleted_successfully' => 'Zone deleted successfully',
  'LTR' => 'LTR',
  'RTL' => 'RTL',
  '  +data[count].name +  ' => '  +data[count].name +  ',
  ' For ' => ' For ',
  'Card Payment' => 'Card Payment',
  'Amount to be paid' => 'Amount to be paid',
  'Buyer Details' => 'Buyer Details',
  'Card Details' => 'Card Details',
  'Pay' => 'Pay',
  'Loading, please wait' => 'Loading  please wait',
  'Callback Url' => 'Callback Url',
  'store_removed' => 'Store removed',
  'proceed' => 'Proceed',
  'The note must not be greater than 200 characters.' => 'The note must not be greater than 200 characters.',
  'Collect_Cash' => 'Collect Cash',
  'Account transaction information' => 'Account transaction information',
  'This value is the minimum distance for a vehicle in this category to serve an order.' => 'This value is the minimum distance for a vehicle in this category to serve an order.',
  'This value is the miximum distance for a vehicle in this category to serve an order.
                                            ' => 'This value is the miximum distance for a vehicle in this category to serve an order.
                                            ',
  'Vehicle_category_created' => 'Vehicle category created',
  'all_user' => 'All user',
  'customer_verification_toggle' => 'Customer verification toggle',
  'If_enabled,_delivery_men_have_to_provide_collected_cash_by_themselves.' => 'If enabled  delivery men have to provide collected cash by themselves.',
  'If_disabled,_delivery_men_do_not_have_to_provide_collected_cash_by_themselves.' => 'If disabled  delivery men do not have to provide collected cash by themselves.',
  'all_module_type' => 'All module type',
  'Blocked_deliveryman' => 'Blocked deliveryman',
  'Total_Completed_Orders' => 'Total Completed Orders',
  'ex_: search_delivery_man_,_email_or_phone' => 'Ex : search delivery man   email or phone',
  'Salary_Base' => 'Salary Base',
  'no_conversation_found' => 'No conversation found',
  'Account not found' => 'Account not found',
  'join_request_date' => 'Join request date',
  'Freelancer' => 'Freelancer',
  'All_Job_Types' => 'All Job Types',
  'The password must contain symbols' => 'The password must contain symbols',
  'The password is compromised. Please choose a different one' => 'The password is compromised. Please choose a different one',
  'application_placed_successfully' => 'Application placed successfully',
  'job_type' => 'Job type',
  'Edit' => 'Edit',
  'reject' => 'Reject',
  'edit-information' => 'Edit-information',
  'Rating' => 'Rating',
  'order_ID' => 'Order ID',
  'cancel_order' => 'Cancel order',
  'completed_order' => 'Completed order',
  'ongoing_order' => 'Ongoing order',
  'delivery_date' => 'Delivery date',
  'total_items' => 'Total items',
  'All_DeliveryMan' => 'All DeliveryMan',
  'Suspended' => 'Suspended',
  'Order_ID' => 'Order ID',
  'Offline' => 'Offline',
  'Online' => 'Online',
  'All_Types' => 'All Types',
  'Completed_orders' => 'Completed orders',
  'ex_: search_by_customer_name' => 'Ex : search by customer name',
  'Customer_Personal_Info' => 'Customer Personal Info',
  'Item_Quantity' => 'Item Quantity',
  'Joining_date' => 'Joining date',
  'addresses' => 'Addresses',
  'office' => 'Office',
  'store_not_found' => 'Store not found',
  'product_count' => 'Product count',
  'ex_: search_by_order_id' => 'Ex : search by order id',
  'create_coupon' => 'Create coupon',
  'total_Items' => 'Total Items',
  'search_by_name_phone_or_email' => 'Search by name phone or email',
  'All_transactions' => 'All transactions',
  'receiver_name' => 'Receiver name',
  'sender_name' => 'Sender name',
  'Inactive_Customers' => 'Inactive Customers',
  'item_quantity' => 'Item quantity',
  'Active_Customers' => 'Active Customers',
  'add_fund_type' => 'Add fund type',
  'select_duration' => 'Select duration',
  'All_Customers' => 'All Customers',
  'fund_statistics' => 'Fund statistics',
  'Order place ($ 1,109)' => 'Order place ($ 1 109)',
  'Loyalty Point ($ 1,100)' => 'Loyalty Point ($ 1 100)',
  'Order place' => 'Order place',
  'Order Refund' => 'Order Refund',
  'Admin Add Fund' => 'Admin Add Fund',
  'Order Refund ($ 1,500)' => 'Order Refund ($ 1 500)',
  'Fund added by Admin ($1,123)' => 'Fund added by Admin ($1 123)',
  'Order place ($1109)' => 'Order place ($1109)',
  'Loyalty Point ($1100)' => 'Loyalty Point ($1100)',
  'Order Refund ($1500)' => 'Order Refund ($1500)',
  'Fund added by Admin ($1123)' => 'Fund added by Admin ($1123)',
  'customer_info' => 'Customer info',
  'New_Customers' => 'New Customers',
  'Customer_info' => 'Customer info',
  'current_points_in_wallet' => 'Current points in wallet',
  'points_converted' => 'Points converted',
  'points_earned' => 'Points earned',
  'transaction_ID' => 'Transaction ID',
  'current_Points_in_Wallet' => 'Current Points in Wallet',
  'points_Converted' => 'Points Converted',
  'Search_by_product_name_or_bar_code' => 'Search by product name or bar code',
  'customer_Information' => 'Customer Information',
  'QTY' => 'QTY',
  'paid_By' => 'Paid By',
  ' pin the address in the map to calculate delivery fee' => ' pin the address in the map to calculate delivery fee',
  'statistics by zone' => 'Statistics by zone',
  'Monitor your' => 'Monitor your',
  'suspended' => 'Suspended',
  'in_Active' => 'In Active',
  'transaction_History' => 'Transaction History',
  'curl_enabled' => 'Curl enabled',
  'Enabled' => 'Enabled',
  'curl' => 'Curl',
  'bcmath' => 'Bcmath',
  'ctype' => 'Ctype',
  'json' => 'Json',
  'mbstring' => 'Mbstring',
  'openssl' => 'Openssl',
  'pdo' => 'Pdo',
  'tokenizer' => 'Tokenizer',
  'xml' => 'Xml',
  'zip' => 'Zip',
  'fileinfo' => 'Fileinfo',
  'gd' => 'Gd',
  'sodium' => 'Sodium',
  'pdo_mysql' => 'Pdo mysql',
  'points_Earned' => 'Points Earned',
  'denial_note' => 'Denial note',
  'approval_note' => 'Approval note',
  'store_Info' => 'Store Info',
  'account_Transaction_Information' => 'Account Transaction Information',
  'approved_Note' => 'Approved Note',
  'owner_Info' => 'Owner Info',
  'withdraw_Amount' => 'Withdraw Amount',
  'add_New_method' => 'Add New method',
  'withdraw_method_list' => 'Withdraw method list',
  'Search_by_ID_or_name' => 'Search by ID or name',
  'see_all' => 'See all',
  'method_Name' => 'Method Name',
  'withdraw_Method_List' => 'Withdraw Method List',
  'stock_Update' => 'Stock Update',
  'loyalty_point_transaction_history' => 'Loyalty point transaction history',
  'transaction_date' => 'Transaction date',
  'Fund added by Admin' => 'Fund added by Admin',
  'ex_: search_email' => 'Ex : search email',
  'earned' => 'Earned',
  'converted' => 'Converted',
  'Top_Completed_Orderer_Customers' => 'Top Completed Orderer Customers',
  'least_Orderer_customers' => 'Least Orderer customers',
  'If you want to make a customized COUPON for this customer, click the Create Coupon button and influence them buy more from your store.' => 'If you want to make a customized COUPON for this customer, click the Create Coupon button and influence them buy more from your store.',
  'sender_info' => 'Sender info',
  'Select_coupon_type' => 'Select coupon type',
  'Currently_you_need_to_manage_discount_with_the_Store.' => 'Currently you need to manage discount with the Store.',
  'Ex:_Coupon_Title_Or_Code' => 'Ex: Coupon Title Or Code',
  'add_to_fund' => 'Add to fund',
  'category_deleted' => 'Category deleted',
  'store deleted!' => 'Store deleted!',
  'refund requested' => 'Refund requested',
  'Refund Image' => 'Refund Image',
  'Method' => 'Method',
  'Admin Note' => 'Admin Note',
  'Customer Note' => 'Customer Note',
  'you_want_to_refund_this_order' => 'You want to refund this order',
  'are_you_sure_want_to_refund' => 'Are you sure want to refund',
  'Cancel Refund' => 'Cancel Refund',
  'Deliveryman_Review' => 'Deliveryman Review',
  'Order#' => 'Order#',
  'Review' => 'Review',
  'withdraw_Information' => 'Withdraw Information',
  'Requested_to_join_at' => 'Requested to join at',
  'Job_Type' => 'Job Type',
  'Identity_Documents' => 'Identity Documents',
  'Identity_Information' => 'Identity Information',
  'Store_Balance' => 'Store Balance',
  'Type_a_note_about_request_approval' => 'Type a note about request approval',
  'Type_a_note_about_request_denial' => 'Type a note about request denial',
  'seller_payment_denied' => 'Seller payment denied',
  'Dental_Note' => 'Dental Note',
  'seller_payment_approved' => 'Seller payment approved',
  'store_withdraw_transactions' => 'Store withdraw transactions',
  'request_status' => 'Request status',
  'requested_amount' => 'Requested amount',
  'owner_name' => 'Owner name',
  'bank_account_no.' => 'Bank account no.',
  'clicking_on_the_map_will_set_Latitude_and_Longitude_automatically' => 'Clicking on the map will set Latitude and Longitude automatically',
  'Additional Charge' => 'Additional Charge',
  'Shipping Charge' => 'Shipping Charge',
  'pages' => 'Pages',
  'system' => 'System',
  'positive_review_given_total' => 'Positive review given total',
  'Scale: 4-5' => 'Scale: 4-5',
  'good_review_given_total' => 'Good review given total',
  'Scale: 3' => 'Scale: 3',
  'neutral_review_given_total' => 'Neutral review given total',
  'Scale: 2' => 'Scale: 2',
  'negative_review_given_total' => 'Negative review given total',
  'Scale: 1' => 'Scale: 1',
  'Required.' => 'Required.',
  'Select_deliveryman_type' => 'Select deliveryman type',
  'select_identity_type' => 'Select identity type',
  'Max_5_Identity_Images' => 'Max 5 Identity Images',
  'The password is required' => 'The password is required',
  'The password must be at least :min characters long' => 'The password must be at least :min characters long',
  'The password must contain both uppercase and lowercase letters' => 'The password must contain both uppercase and lowercase letters',
  'The password must contain letters' => 'The password must contain letters',
  'The password must contain numbers' => 'The password must contain numbers',
  'The password cannot contain white spaces.' => 'The password cannot contain white spaces.',
  'Select a vehicle' => 'Select a vehicle',
  'no_review/rating_given_yet' => 'No review/rating given yet',
  'Total_Deliveryman' => 'Total Deliveryman',
  'By_Turning_ON_Vehicle_Category!' => 'By Turning ON Vehicle Category!',
  'By_Turning_OFF_Vehicle_Category!' => 'By Turning OFF Vehicle Category!',
  'Turned_on_this_vehicle_category_extra_charge_will_be_added_on_the_delivery_charge_and_this_categories_deliverymen_can_receives_the_order.' => 'Turned on this vehicle category extra charge will be added on the delivery charge and this categories deliverymen can receives the order.',
  'Turned_off_this_vehicle_category_extra_charge_will_not_be_added_on_the_delivery_charge_and_this_categories_deliverymen_can_not_receives_the_order' => 'Turned off this vehicle category extra charge will not be added on the delivery charge and this categories deliverymen can not receives the order',
  'edit_vehicle_category' => 'Edit vehicle category',
  'Want_to_delete_this_vehicle_category' => 'Want to delete this vehicle category',
  'delete_vehicle_category' => 'Delete vehicle category',
  'select_customer_by_name_or_phone' => 'Select customer by name or phone',
  'Ex: 50' => 'Ex: 50',
  'Ex: 123' => 'Ex: 123',
  'Add_Fund_To_Wallet' => 'Add Fund To Wallet',
  'all_type' => 'All type',
  'You_can’t_reply_to_this_conversation.' => 'You can’t reply to this conversation.',
  'Learn_more' => 'Learn more',
  'You can’t chat with deliveryman because it’s delivery man previous chat history, only you can monitor or view their conversation to avoid unexpected situation.' => 'You can’t chat with deliveryman because it’s delivery man previous chat history  only you can monitor or view their conversation to avoid unexpected situation.',
  'DeliveryMan_Info' => 'DeliveryMan Info',
  'Default_title_is_required' => 'Default title is required',
  'Default_subtitle_is_required' => 'Default subtitle is required',
  'Banner_image_is_required' => 'Banner image is required',
  'Banner image is required' => 'Banner image is required',
  'All Type' => 'All Type',
  'flash_sale_discount' => 'Flash sale discount',
  'Other_Expenses' => 'Other Expenses',
  'expense_reports' => 'Expense reports',
  'all_time' => 'All time',
  'your_account_has_been_blocked' => 'Your account has been blocked',
  'Set_permission' => 'Set permission',
  'Permissions' => 'Permissions',
  'sell_count' => 'Sell count',
  'Search_by_product_name' => 'Search by product name',
  'Unit_price' => 'Unit price',
  'By_turning_the_‘Maintenance_Mode’_ON,_all_your_apps_and_customer_website_will_be_disabled_temporarily._Only_the_Admin_Panel,_Admin_Landing_Page_&_Store_Panel_will_be_functional.' => 'By turning the ‘Maintenance Mode’ ON  all your apps and customer website will be disabled temporarily. Only the Admin Panel  Admin Landing Page & Store Panel will be functional.',
  'fewfwe' => 'Fewfwe',
  'ex:_DM_name_email_or_phone' => 'Ex: DM name,email or phone',
  'ex:_name_email_or_phone' => 'Ex: name, email or phone',
  'Add Info From Gallery' => 'Add Info From Gallery',
  'Custom_Date' => 'Custom Date',
  'restaurant' => 'Restaurant',
  'This item is under review' => 'This item is under review',
  'Food Bulk Export' => 'Food Bulk Export',
  'items_bulk_export' => 'Items bulk export',
  'Step 1' => 'Step 1',
  'Instruction' => 'Instruction',
  'Select_data_type_in_which_order_you_want_your_data_sorted_while_downloading.' => 'Select data type in which order you want your data sorted while downloading.',
  'Step 2' => 'Step 2',
  'Select Data Range by Date or ID and Export' => 'Select Data Range by Date or ID and Export',
  'The_file_will_be_downloaded_in_.xls_format' => 'The file will be downloaded in .xls format',
  'Click_reset_if_you_want_to_clear_you_changes_and_want_to_download_in_default_sort_wise_data' => 'Click reset if you want to clear you changes and want to download in default sort wise data',
  'Download_Excel_File' => 'Download Excel File',
  'Download_the_format_file_and_fill_it_with_proper_data.' => 'Download the format file and fill it with proper data.',
  'You_can_download_the_example_file_to_understand_how_the_data_must_be_filled.' => 'You can download the example file to understand how the data must be filled.',
  'Have_to_upload_excel_file.' => 'Have to upload excel file.',
  'Match_Spread_sheet_data_according_to_instruction' => 'Match Spread sheet data according to instruction',
  'Fill_up_the_data_according_to_the_format_and_validations.' => 'Fill up the data according to the format and validations.',
  'You_can_get_store_id_module_id_and_unit_id_from_their_list_please_input_the_right_ids.' => 'You can get store id module id and unit id from their list please input the right ids.',
  'For_ecommerce_item_avaliable_time_start_and_end_will_be_00:00:00_and_23:59:59' => 'For ecommerce item avaliable time start and end will be 00:00:00 and 23:59:59',
  'If_you_want_to_create_a_product_with_variation,_just_create_variations_from_the_generate_variation_section_below_and_click_generate_value.' => 'If you want to create a product with variation  just create variations from the generate variation section below and click generate value.',
  'Copy_the_value_and_paste_the_the_spread_sheet_file_column_name_variation_in_the_selected_product_row.' => 'Copy the value and paste the the spread sheet file column name variation in the selected product row.',
  'Step 3' => 'Step 3',
  'In_the_Excel_file_upload_section,_first_select_the_upload_option.' => 'In the Excel file upload section  first select the upload option.',
  'Upload_your_file_in_.xls,_.xlsx_format.' => 'Upload your file in .xls  .xlsx format.',
  'Finally_click_the_upload_button.' => 'Finally click the upload button.',
  'You_can_upload_your_product_images_in_product_folder_from_gallery_and_copy_image`s_path.' => 'You can upload your product images in product folder from gallery and copy image`s path.',
  'With Current Data' => 'With Current Data',
  'Without Any Data' => 'Without Any Data',
  'Select_Data_Upload_type' => 'Select Data Upload type',
  'Upload_New_Data' => 'Upload New Data',
  'Update_Existing_Data' => 'Update Existing Data',
  'Import_items_file' => 'Import items file',
  'Must_be_Excel_files_using_our_Excel_template_above' => 'Must be Excel files using our Excel template above',
  'Upload' => 'Upload',
  'Generate Value' => 'Generate Value',
  'Generate Variation' => 'Generate Variation',
  'Attention!' => 'Attention!',
  'You_must_generate_variations_from_this_generator_if_you_want_to_add_variations_to_your_products.You_must_copy_from_the_specific_filed_and_past_it_to_the_specific_column_at_your_excel_sheet.Otherwise_you_might_get_500_error_if_you_swap_or_entered_invalid_data.And_if_you_want_to_make_it_empty_then_you_have_to_enter_an_empty_array_[_]_.' => 'You must generate variations from this generator if you want to add variations to your products.You must copy from the specific filed and past it to the specific column at your excel sheet.Otherwise you might get 500 error if you swap or entered invalid data.And if you want to make it empty then you have to enter an empty array [ ] .',
  'generate value' => 'Generate value',
  'Generated_varient' => 'Generated varient',
  'This_field_is_for_geenrated_variation._copy_them_&_paste_into_excel_sheet' => 'This field is for geenrated variation. copy them & paste into excel sheet',
  'Generated_choice_option' => 'Generated choice option',
  'Choice_option_is_required_if_you_are_using_product_variation' => 'Choice option is required if you are using product variation',
  'Generated_attributes_field' => 'Generated attributes field',
  'Attributes_is_required_if_you_are_using_product_variation' => 'Attributes is required if you are using product variation',
  'Data.' => 'Data.',
  'Fill_up_the_data_according_to_the_format.' => 'Fill up the data according to the format.',
  'Make_sure_the_phone_numbers_and_email_addresses_are_unique.' => 'Make sure the phone numbers and email addresses are unique.',
  'You_can_get_module_id_and_zone_id_from_their_list,_please_input_the_right_ids.' => 'You can get module id and zone id from their list  please input the right ids.',
  'For_delivery_time_the_format_is_"from-to_type"_for_example:_"30-40_min"._Also_you_can_use_days_or_hours_as_type._Please_be_carefull_about_this_format_or_leave_this_field_empty.' => 'For delivery time the format is  from-to type  for example:  30-40 min . Also you can use days or hours as type. Please be carefull about this format or leave this field empty.',
  'Latitude_must_be_a_number_between_-90_to_90_and_Longitude_must_a_number_between_-180_to_180._Otherwise_it_will_create_server_error' => 'Latitude must be a number between -90 to 90 and Longitude must a number between -180 to 180. Otherwise it will create server error',
  'After_uploading_stores_you_need_to_edit_them_and_set_stores`s_logo_and_cover.`s_path' => 'After uploading stores you need to edit them and set stores`s logo and cover.`s path',
  'You_can_upload_your_store_images_in_store_folder_from_gallery,_and_copy_image`s_path' => 'You can upload your store images in store folder from gallery  and copy image`s path',
  'Default_password_for_store_is_12345678.' => 'Default password for store is 12345678.',
  'Import_Stores_file' => 'Import Stores file',
  'Out_of_Stock' => 'Out of Stock',
  'Stock_Out' => 'Stock Out',
  'Total_orders' => 'Total orders',
  'High_to_Low' => 'High to Low',
  'Low_to_High' => 'Low to High',
  'Payment_By' => 'Payment By',
  'ssl_commerz_payment' => 'Ssl commerz payment',
  'receiver' => 'Receiver',
  'Sender' => 'Sender',
  'Receiver' => 'Receiver',
  'Price Information' => 'Price Information',
  'thumbnail image is required' => 'Thumbnail image is required',
  'Today' => 'Today',
  'Make Sure' => 'Make Sure',
  'All of your module details should be well-structured. Because those details are dynamically shown on the Landing page of your business.' => 'All of your module details should be well-structured. Because those details are dynamically shown on the Landing page of your business.',
  'Don’t_forget_to_click_the_‘Add_Module’_button_below_to_save_the_new_business_module' => 'Don’t forget to click the ‘Add Module’ button below to save the new business module',
  'basic_setup' => 'Basic setup',
  'Chose related images' => 'Chose related images',
  'The module type field is required.' => 'The module type field is required.',
  'Default name is required' => 'Default name is required',
  'Default description is required' => 'Default description is required',
  'The module name has already been taken.' => 'The module name has already been taken.',
  'module_created_successfully' => 'Module created successfully',
  'The icon field is required.' => 'The icon field is required.',
  'The thumbnail field is required.' => 'The thumbnail field is required.',
  'Ex:_Referance,_Name' => 'Ex: Referance',
  'Starting_coverage_area' => 'Starting coverage area',
  'the_starting_coverage_area_represents_the_location_where_deliveries_are_made.' => 'The starting coverage area represents the location where deliveries are made.',
  'the_maximum_coverage_area_represents_the_farthest_or_widest_extent_to_which_deliveries_can_be_made' => 'The maximum coverage area represents the farthest or widest extent to which deliveries can be made',
  'the_starting_coverage_area_represents_the_location_where_deliveries_are_made' => 'The starting coverage area represents the location where deliveries are made',
  'Vehicle_status_updated' => 'Vehicle status updated',
  'default_type_is_required' => 'Default type is required',
  'By_Turning_ON_Campaign!' => 'By Turning ON Campaign!',
  'By_Turning_OFF_Campaign!' => 'By Turning OFF Campaign!',
  'Turned_on_to_customer_website_and_apps._Are_you_sure_you_want_to_turn_on_the_campaign_already_inactive.' => 'Turned on to customer website and apps. Are you sure you want to turn on the campaign already inactive.',
  'Turned_off_to_customer_website_and_apps._Are_you_sure_you_want_to_turn_off_the_campaign_already_active' => 'Turned off to customer website and apps. Are you sure you want to turn off the campaign already active',
  'If_you_turn_on_this_status,_it_will_show_on_user_website_and_app.' => 'If you turn on this status  it will show on user website and app.',
  'If_you_turn_off_this_status,_it_won’t_show_on_user_website_and_app' => 'If you turn off this status  it won’t show on user website and app',
  'By_Turning_ON_Banner!' => 'By Turning ON Banner!',
  'By_Turning_OFF_Banner!' => 'By Turning OFF Banner!',
  'Turned_on_to_customer_website_and_apps._Are_you_sure_you_want_to_turn_on_the_Banner_already_inactive.' => 'Turned on to customer website and apps. Are you sure you want to turn on the Banner already inactive.',
  'Turned_off_to_customer_website_and_apps._Are_you_sure_you_want_to_turn_off_the_Banner_already_active' => 'Turned off to customer website and apps. Are you sure you want to turn off the Banner already active',
  'if_you_turn/off_on_this_featured,_it_will_effect_on_website_&_user_app' => 'If you turn/off on this featured  it will effect on website & user app',
  'If_you_turn_on_this_featured,_then_promotional_banner_will_show_on_website_and_user_app_with_store_or_item.' => 'If you turn on this featured  then promotional banner will show on website and user app with store or item.',
  'If_you_turn_off_this_featured,_then_promotional_banner_won’t_show_on_website_and_user_app' => 'If you turn off this featured  then promotional banner won’t show on website and user app',
  'By_Turning_ON_As_Featured!' => 'By Turning ON As Featured!',
  'By_Turning_OFF_As_Featured!' => 'By Turning OFF As Featured!',
  'If_you_enable_this,_any_store_can_duplicate_product_and_create_a_new_product_by_use_this.' => 'If you enable this  any store can duplicate product and create a new product by use this.',
  'If_you_enable_this_Stores_can_access_all_products_of_other_stores.' => 'If you enable this Stores can access all products of other stores.',
  'parcel45436534' => 'Parcel45436534',
  'Schedule_Order' => 'Schedule Order',
  'Scheduled Order?' => 'Scheduled Order ',
  'If you disable this, the Scheduled Order feature will be hidden.' => 'If you disable this  the Scheduled Order feature will be hidden.',
  'Password_Reset_Request' => 'Password Reset Request',
  'New Password' => 'New Password',
  'Change Password' => 'Change Password',
  'invalid_customer' => 'Invalid customer',
  'need_to_check_minimum_1_criteria_for_approval' => 'Need to check minimum 1 criteria for approval',
  'need_to_check_minimum_1_criteria_for_product_approval' => 'Need to check minimum 1 criteria for product approval',
  'Demo Module' => 'Demo Module',
  'Ex: ABC Company' => 'Ex: ABC Company',
  'Select_zone_first' => 'Select zone first',
  'Upload Cover Photo' => 'Upload Cover Photo',
  'alphanet_sms' => 'Alphanet sms',
  '[Apple_Developer_Account_Name - Team_ID]' => '[Apple Developer Account Name - Team ID]',
  'not_editable' => 'Not editable',
  'ReCAPTCHA Failed' => 'ReCAPTCHA Failed',
  'The phone must be at least 10 characters.' => 'The phone must be at least 10 characters.',
  '*Customers_cannot_request_a_Refund_if_the_Admin_does_not_specify_a_cause_for_refund_even_though_they_see_the_Refund_option._So_Admin_MUST_provide_a_proper_Refund_Reason._At_least_one_reason_Must_be_ON_in_the_reason_list.' => '*Customers cannot request a Refund if the Admin does not specify a cause for refund even though they see the Refund option. So Admin MUST provide a proper Refund Reason. At least one reason Must be ON in the reason list.',
  'deliveryman_description' => 'Deliveryman description',
  'Write_a_short_description_of_your_experience_in delivry_field_within_100_words_(550_characters)' => 'Write a short description of your experience in delivry field within 100 words (550 characters)',
  'Write_a_short_description_of_your_experience_in_delivery_field_within_100_words_(550_characters)' => 'Write a short description of your experience in delivery field within 100 words (550 characters)',
  'DeliveryMan_description' => 'DeliveryMan description',
  'Deliveryman_description' => 'Deliveryman description',
  'description_is_required' => 'Description is required',
  'Edit Information' => 'Edit Information',
  'Short_Bio' => 'Short Bio',
  'Store_&_Category_Info' => 'Store & Category Info',
  'Image is required!' => 'Image is required!',
  'customer_added_successfully' => 'Customer added successfully',
  'please_select_a_valid_delivery_location_on_the_map' => 'Please select a valid delivery location on the map',
  'set_admin_store_commission' => 'Set admin store commission',
  'set_admin_delivery_charge_comission' => 'Set admin delivery charge comission',
  'enter_percentage' => 'Enter percentage',
  'Commission_Rate_On_Order' => 'Commission Rate On Order',
  'Commission_Rate_On_Delivery' => 'Commission Rate On Delivery',
  'Food_module_maximum_amount_of_order' => 'Food module maximum amount of order',
  'Set_up_‘Maximum_amount’_on_Order_of_Food_module_._When_amount_of_order_is_more_then_this_maximum_amount_customer_has_free_delivery_and_free_service.' => 'Set up ‘Maximum amount’ on Order of Food module . When amount of order is more then this maximum amount customer has free delivery and free service.',
  'Others_modules_maximum_amount_of_order' => 'Others modules maximum amount of order',
  'Set_up_‘Maximum_amount’_on_Order_of_Others_modules_._When_amount_of_order_is_more_then_this_maximum_amount_customer_has_free_delivery_and_free_service.' => 'Set up ‘Maximum amount’ on Order of Others modules . When amount of order is more then this maximum amount customer has free delivery and free service.',
  'take_away_is_not_active' => 'Take away is not active',
  'Credential_do_not_match,_please_try_again' => 'Credential do not match  please try again',
  'store_is_closed_at_order_time' => 'Store is closed at order time',
  'End time must be after the start time' => 'End time must be after the start time',
  'The minimum order field is required.' => 'The minimum order field is required.',
  'no_payment_gateway_found' => 'No payment gateway found',
  'seventh_order_promotion' => 'Seventh order promotion',
  'If_enabled,_customers_have_a_free_delivery_on_seventh_order.' => 'If enabled  customers have a free delivery on seventh order.',
  'customer_free_delivery_toggle' => 'Customer free delivery toggle',
  'Want_to_enable_seventh_order_promotion?' => 'Want to enable seventh order promotion ',
  'Want_to_disable_seventh_order_promotion?' => 'Want to disable seventh order promotion ',
  'If_you_enable_this,_customers_have_a_free_delivery_on_seventh_order.' => 'If you enable this  customers have a free delivery on seventh order.',
  'If_you_disable_this,_customers_do_not_have_a_free_delivery_on_seventh_order.' => 'If you disable this  customers do not have a free delivery on seventh order.',
  'Add a Delivery Instruction' => 'Add a Delivery Instruction',
  'Write_the_short_description_within_191_characters' => 'Write the short description within 191 characters',
  'Ex:_parcel_contains_document' => 'Ex: parcel contains document',
  'Delivery Instruction List' => 'Delivery Instruction List',
  'max_cash_in_hand_exceeds' => 'Max cash in hand exceeds',
  'Customer_can_give_tips_to_deliveryman_during_checkout_from_the_customer_app_&_website._From_this,_admin_has_no_commission.' => 'Customer can give tips to deliveryman during checkout from the customer app & website. From this  admin has no commission.',
  'can_not_accept' => 'Can not accept',
  'location_not_found' => 'Location not found',
  'dm_maximum_order_exceed_warning' => 'Dm maximum order exceed warning',
  'The email has already been taken.' => 'The email has already been taken.',
  'store_added_successfully' => 'Store added successfully',
  'service_file_content' => 'Service file content',
  'select and copy all the service file content and add here' => 'Select and copy all the service file content and add here',
  'message.is_promotion' => 'Message.is promotion',
  'is_promotion' => 'Is promotion',
  'service_fee_promotion' => 'Service fee promotion',
  'Set_up_Service_fee_for_promotion.' => 'Set up Service fee for promotion.',
  'Delay_of_sending_notification' => 'Delay of sending notification',
  'secondes' => 'Secondes',
  'Set_up_delay_to_send_a_notifcation_to_deliveryman.' => 'Set up delay to send a notifcation to deliveryman.',
  'Ex:_30' => 'Ex: 30',
  'order_already_assign_to_this_deliveryman' => 'Order already assign to this deliveryman',
  'you_can_not_change_the_status_of_this_order' => 'You can not change the status of this order',
  'order_note' => 'Order note',
  'Grocery_2' => 'Grocery 2',
  'pricing' => 'Pricing',
  'please_select_zone' => 'Please select zone',
  'min_distance' => 'Min distance',
  'max_distance' => 'Max distance',
  'unit_price' => 'Unit price',
  'pricing_added_successfully' => 'Pricing added successfully',
  'min_distance is required!' => 'Min distance is required!',
  'max_distance is required!' => 'Max distance is required!',
  'unit price is required!' => 'Unit price is required!',
  'unit_price_and_distances_was_added_successfully' => 'Unit price and distances was added successfully',
  'Want to delete this pricing ?' => 'Want to delete this pricing  ',
  'update_pricing' => 'Update pricing',
  'price_and_distances_was_updated_successfully' => 'Price and distances was updated successfully',
  'Want_to_Delete_this_Pricing?' => 'Want to Delete this Pricing ',
  'If_yes_,_pricing_will_be_DELETED_FOREVER.' => 'If yes   pricing will be DELETED FOREVER.',
  'delete_pricing' => 'Delete pricing',
  'price_and_distances_was_deleted_successfully' => 'Price and distances was deleted successfully',
  'min_distance_(Km)' => 'Min distance (Km)',
  'max_distance_(Km)' => 'Max distance (Km)',
  'min_and_max_distance_is_existed!!' => 'Min and max distance is existed!!',
  'Please select zone' => 'Please select zone',
  'min distance is required!' => 'Min distance is required!',
  'max distance is required!' => 'Max distance is required!',
  'min_and_max_distance_is_existed_or_incorrect_data!!' => 'Min and max distance is existed or incorrect data!!',
  'all_conditions' => 'All conditions',
  'Incorrect_credential,_please_try_again' => 'Incorrect credential  please try again',
  'parcel_image' => 'Parcel image',
  'Delay_of_sending_multi_orders_notification' => 'Delay of sending multi orders notification',
  'Set_up_delay_to_send_multi_orders_a_notifcation_to_deliveryman.' => 'Set up delay to send multi orders a notifcation to deliveryman.',
  349857 => '349857',
  'If_enabled,_deliverymen_will_receive_an_email_for_account_suspension' => 'If enabled  deliverymen will receive an email for account suspension',
  '$105' => '$105',
  'campaign_is_expired' => 'Campaign is expired',
  'amount_paid_to_store' => 'Amount paid to store',
  'amount_paid_to_deliveryman' => 'Amount paid to deliveryman',
  'commission_amount_of_admin' => 'Commission amount of admin',
  'cash_in_hand_of_this_store' => 'Cash in hand of this store',
  'commission_amount_of_admin_from_store' => 'Commission amount of admin from store',
  'commission_amount_of_admin_from_delivery_man' => 'Commission amount of admin from delivery man',
  'push_notification_faild' => 'Push notification faild',
  'amount_of_take_away_on_cash_in_hand_of_store' => 'Amount of take away on cash in hand of store',
  'threshold_Of_Difference_Time_Between_Orders' => 'Threshold Of Difference Time Between Orders',
  'Set_up_threshold_of_difference_time_between_new_order_and_orders_have_accepted_by_deliverymen.' => 'Set up threshold of difference time between new order and orders have accepted by deliverymen.',
  'max_amount_admin_commission_from_deliveryman' => 'Max amount admin commission from deliveryman',
  'Set_up_max_amount_of_commissions_admin_has_taken_from_deliveryman.' => 'Set up max amount of commissions admin has taken from deliveryman.',
  'Ex:_50' => 'Ex: 50',
  'When_enabled,_admin_will_only_receive_the_certain_commission_percentage_he_set_for_this_deliveryman._Otherwise,_the_system_default_commission_will_be_applied.' => 'When enabled  admin will only receive the certain commission percentage he set for this deliveryman. Otherwise  the system default commission will be applied.',
  'Set_up_max_amount_of_admin_commission_has_taken_from_deliveryman.' => 'Set up max amount of admin commission has taken from deliveryman.',
  'first_time_orders' => 'First time orders',
  'initial' => 'Initial',
  'max_distance_proximity_between_the_centroid_and_the_three_users_locations' => 'Max distance proximity between the centroid and the three users locations',
  'Kilometers' => 'Kilometers',
  'Set_up_max_distance_proximity_between_the_centeroid_and_the_three_users_locations.' => 'Set up max distance proximity between the centeroid and the three users locations.',
  'Ex:_1.00' => 'Ex: 1.00',
  'Change status to initial ?' => 'Change status to initial  ',
  'first_order_status' => 'First order status',
  'When_admin_enable_first_order,_the_first_order_of_new_Customer_will_display_in_initial_list_of_admin_panel_inside_the_order_section.' => 'When admin enable first order  the first order of new Customer will display in initial list of admin panel inside the order section.',
  'order_status_toggle' => 'Order status toggle',
  'initial_status?' => 'Initial status ',
  'If you disable this, the first order of new Customer will display in initial list of admin panel inside the order section.' => 'If you disable this  the first order of new Customer will display in initial list of admin panel inside the order section.',
  'When_admin_enable_first_order_status,_the_first_order_of_new_Customer_will_display_in_initial_list_of_admin_panel_inside_the_order_section.' => 'When admin enable first order status  the first order of new Customer will display in initial list of admin panel inside the order section.',
  'If you enable this, the first order of new Customer will display in initial list of admin panel inside the order section.' => 'If you enable this  the first order of new Customer will display in initial list of admin panel inside the order section.',
  'If you disable this, the first order of new Customer will not display in initial list of admin panel inside the order section.' => 'If you disable this  the first order of new Customer will not display in initial list of admin panel inside the order section.',
  'Desserts and drinks' => 'Desserts and drinks',
  'notification_updated_successfully' => 'Notification updated successfully',
  'Its_an_error_on_the_server_side_please_try_again_later' => 'It\'s an error on the server side please try again later',
  'Firebase OTP Verification' => 'Firebase OTP Verification',
  'If_you_activate_this_feature,_customers_need_to_verify_their_account_information_via_firebase_OTP_during_the_signup_process.' => 'If you activate this feature  customers need to verify their account information via firebase OTP during the signup process.',
  'Firebase_OTP_Verification?' => 'Firebase OTP Verification ',
  'If_you_enable_this,_Customers_have_to_verify_their_account_via_Firebase_OTP.' => 'If you enable this  Customers have to verify their account via Firebase OTP.',
  'If_you_disable_this,_Customers_don’t_need_to_verify_their_account_via_Firebase_OTP.' => 'If you disable this  Customers don’t need to verify their account via Firebase OTP.',
  'Desserts' => 'Desserts',
);